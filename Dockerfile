FROM 559104660845.dkr.ecr.eu-west-1.amazonaws.com/amazoncorretto:23-alpine3.21-jdk

LABEL maintainer="<EMAIL>"

EXPOSE 8080

VOLUME /tmp

ADD target/ms-partner-ordering-1.0.0.jar ms-partner-ordering.jar

RUN /bin/sh -c 'touch /ms-partner-ordering.jar'

RUN apk add --no-cache msttcorefonts-installer fontconfig
RUN update-ms-fonts

ENV TZ=Africa/Nairobi

ENV NO_PROXY="*.safaricom.net,*.safaricom.co.ke,*.kubernetesuat.*"

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

ENTRYPOINT ["java","-Xmx256m", "-XX:+UseG1GC", "-Djava.security.manager=allow", "-Djava.security.egd=file:/dev/./urandom","-jar","/ms-partner-ordering.jar"]
