<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.safaricom.dxl</groupId>
        <artifactId>dxl-webflux-starter-parent</artifactId>
        <version>1.4.7</version>
        <relativePath/>
    </parent>
    <artifactId>ms-partner-ordering</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>ms-partner-ordering</name>
    <description>DxL reactive micro service starter</description>
    <properties>
        <sonar.exclusions>
            pom.xml,
            **/config/**/*,
            **/data/**/*,
            **/exception/*.java,
            **/utils/**/*,
            **/service/**/*,
            **/controller/**/*,
            **/MsStarterApplication.java
        </sonar.exclusions>
        <sonar.coverage.exclusions>
            pom.xml,
            **/config/**/*,
            **/data/**/*,
            **/exception/*.java,
            **/utils/**/*,
            **/service/**/*,
            **/controller/**/*,
            **/MsStarterApplication.java
        </sonar.coverage.exclusions>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-r2dbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.xmlunit</groupId>
                    <artifactId>xmlunit-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.xmlunit/xmlunit-core -->
        <dependency>
            <groupId>org.xmlunit</groupId>
            <artifactId>xmlunit-core</artifactId>
            <version>2.10.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>r2dbc-postgresql</artifactId>
            <version>1.0.7.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.7</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.r2dbc</groupId>
            <artifactId>r2dbc-spi</artifactId>
            <version>1.0.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>io.asyncer</groupId>
            <artifactId>r2dbc-mysql</artifactId>
            <version>1.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20240303</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.ozlerhakan</groupId>
            <artifactId>poiji</artifactId>
            <version>4.6.0</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>2.17.106</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Netty Dependencies -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http2</artifactId>
            <version>4.1.124.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>4.1.124.Final</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>dxl-releases</id>
            <name>safaricom-dxl-releases</name>
            <url>https://jfrog.safaricom.co.ke/artifactory/dxl-releases/</url>
        </repository>
        <repository>
            <id>dxl-snapshots</id>
            <name>safaricom-dxl-snapshot</name>
            <url>https://jfrog.safaricom.co.ke/artifactory/dxl-snapshot/</url>
        </repository>
    </repositories>
</project>