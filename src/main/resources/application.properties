server.port=8080
spring.service.name=ms-partner-ordering
spring.profiles.active=production
spring.config.import=optional:configserver:http://ms-config-server-service:8888
spring.application.name=ws-starter,ms-partner-ordering

#Default Logging Format
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} | Severity=%-5p | MicroService=${spring.service.name} | %m %n
#---------------------------------------------------------------------