package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.PicklistNominationDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

public interface PicklistNominationService {

    public Mono<WsResponse> addNominee(Map<String, String> headers, PicklistNominationDto nominee, String dealerCode);
    public Mono<WsResponse> getActiveNominations(Map<String, String> headers, String dealerCode, int pageNo, int pageSize);
    Mono<WsResponse> retireNomination(Map<String, String> headers, Long nominationId, String dealerCode);

}
