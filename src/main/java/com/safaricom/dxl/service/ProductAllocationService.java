package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.SingleProductAllocationData;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.springframework.http.codec.multipart.FilePart;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Map;

public interface ProductAllocationService {
    Mono<WsResponse> fetchProductAllocationDetailsByDealerCode(Map<String, String> headers, String dealerCode, String productCode);

    Mono<WsResponse> processExcelFile(Map<String, String> headers, FilePart file, LocalDate startDate, LocalDate endDate, String productName, String allocationFor, boolean updateFlag);

    Mono<WsResponse> fetchDealerAllocations(Map<String, String> headers, String dealerCode);

    Mono<WsResponse> allocateSingleProduct(Map<String, String> headers, SingleProductAllocationData data);
}
