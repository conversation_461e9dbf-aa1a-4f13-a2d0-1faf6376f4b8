package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.CartDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface CartService {

    Mono<WsResponse> add(Map<String, String> headers, Mono<CartDto> payload);

    Mono<WsResponse> fetchCartItems(Map<String, String> headers, String dealerCode, String userId);

    Mono<WsResponse> deleteSavedCartItems(Map<String, String> headers, String dealerCode, String savedCartId);
}