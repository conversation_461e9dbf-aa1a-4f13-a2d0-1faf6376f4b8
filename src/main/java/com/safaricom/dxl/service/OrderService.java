package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.CartItemsDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

public interface OrderService {
    Mono<WsResponse> createOrder(Map<String, String> headers, String dealerCode, CartItemsDto orderId);

    Mono<WsResponse> fetchOrderByDealerCode(Map<String, String> headers, String dealerCode, int pageNo, int pageSize, String from, String to);

    Mono<WsResponse> fetchOrderByOrderNumber(Map<String, String> headers, String dealerCode, String orderNumber);

    Mono<WsResponse> exportToS3(Map<String, String> headers, String shortCode, String startDate, String endDate);

    Mono<WsResponse> fetchExcelUrl(Map<String, String> headers, String shortCode);
}
