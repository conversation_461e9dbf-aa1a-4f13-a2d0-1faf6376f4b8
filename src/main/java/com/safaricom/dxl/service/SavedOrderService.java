package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.SaveOrderItemsDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

public interface SavedOrderService {
    Mono<WsResponse> add(Map<String, String> headers, String dealerCode, SaveOrderItemsDto payload);

    Mono<WsResponse> fetchSavedOrder(Map<String, String> headers, String dealerCode, String userId, int pageNo, int pageSize);

    Mono<WsResponse> deleteSavedOrder(Map<String, String> headers, String dealerCode, String orderIdentifier);

    Mono<WsResponse> fetchSavedOrderByOrderIdentifier(Map<String, String> headers, String dealerCode, String orderIdentifier);
}
