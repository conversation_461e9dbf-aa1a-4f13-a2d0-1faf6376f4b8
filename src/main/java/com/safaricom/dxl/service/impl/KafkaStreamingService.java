package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.model.DataStreaming;
import com.safaricom.dxl.webflux.starter.annotation.WsProcess;
import com.safaricom.dxl.webflux.starter.config.WsKafkaProperties;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import com.safaricom.dxl.webflux.starter.service.WsStarterStreamProducer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class KafkaStreamingService {
    private final WsStarterStreamProducer starterStreamProducer;
    private final WsKafkaProperties wsKafkaProperties;
    private final WsStarterService wsStarterService;

    @WsProcess("STREAM_KAFKA_NIFI")
    public void streamToKafka(DataStreaming dataStreaming, long startTime) {
        long responseTime = System.currentTimeMillis() - startTime;
        String json = "";
        if (dataStreaming != null) {
            dataStreaming.setResponseTime(responseTime);
            json = wsStarterService.serializeToJson(dataStreaming);
        }
        starterStreamProducer.produce(wsKafkaProperties.getTopic(), json);
    }
}
