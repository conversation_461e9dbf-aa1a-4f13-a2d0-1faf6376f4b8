package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.CartDto;
import com.safaricom.dxl.data.model.Delete;
import com.safaricom.dxl.data.model.PartnerInfo;
import com.safaricom.dxl.data.model.ValidationContext;
import com.safaricom.dxl.data.postgres.entities.CartEntity;
import com.safaricom.dxl.data.postgres.repositories.CartRepository;
import com.safaricom.dxl.service.CartService;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;

/**
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class CartServiceImpl implements CartService {

    private final WsResponseMapper responseMapper;
    private final InputValidation validate;
    private final Shared shared;
    private final SSOToken ssoToken;
    private final CartRepository cartRepository;

    @Override
    public Mono<WsResponse> add(Map<String, String> headers, CartDto dto) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), null, "add to cart");
        partnerInfo.setDealerCode(dto.getDealerCode());
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_ADD_CART, ERR_VALIDATION, "addToCart");
        return shared.executeWithInputAndSessionValidation(validate.cartPayload(headers, dto),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dto.getDealerCode()),
                streamData -> cartRepository.findByUserIdAndProductCode(Long.parseLong(dto.getUserId()), dto.getProductCode())
                        .flatMap(cartEntity -> cartRepository.save(CartEntity.of(cartEntity, dto))
                                .flatMap(cart -> {
                                    streamData.setReportName("Update Cart");
                                    shared.streamToKafka(headers, streamData, null, null);
                                    return responseMapper.setApiResponse(ERR_SUCCESS, cart, TRANS_ADD_CART, FALSE, headers);
                                }))
                        .switchIfEmpty(Mono.defer(() -> {
                            streamData.setReportName("Save Cart");
                            return cartRepository.save(CartEntity.of(dto))
                                    .flatMap(cart -> {
                                        shared.streamToKafka(headers, streamData, null, null);
                                        return responseMapper.setApiResponse(ERR_SUCCESS, cart, TRANS_ADD_CART, FALSE, headers);
                                    });
                        })), context
        );
    }

    @Override
    public Mono<WsResponse> fetchCartItems(Map<String, String> headers, String dealerCode, String userId) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchCartItems");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_CART, ERR_VALIDATION, "fetchCartItems");
        return shared.executeWithInputAndSessionValidation(validate.fetchCartItemsRequest(headers, dealerCode, userId),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> cartRepository.customFindByUserId(Long.valueOf(userId))
                        .collectList()
                        .flatMap(cartEntities -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, cartEntities, TRANS_FETCH_CART, FALSE, headers);
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> deleteSavedCartItems(Map<String, String> headers, String dealerCode, String savedCartId) {
        long startTime = System.currentTimeMillis();
        PartnerInfo partnerInfo = new PartnerInfo(startTime, dealerCode, "delete cart");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_DELETE_CART, ERR_VALIDATION, "deleteSavedCartItems");
        return shared.executeWithInputAndSessionValidation(validate.deleteSavedCartItemsRequest(headers, dealerCode, savedCartId),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> cartRepository.deleteById(Long.valueOf(savedCartId))
                        .thenReturn(new Delete(true))
                        .flatMap(delete -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, delete, TRANS_DELETE_CART, FALSE, headers);
                        }), context
        );
    }

}