package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.data.dto.SaveOrderItemsDto;
import com.safaricom.dxl.data.model.Delete;
import com.safaricom.dxl.data.model.PaginationList;
import com.safaricom.dxl.data.model.PartnerInfo;
import com.safaricom.dxl.data.model.ValidationContext;
import com.safaricom.dxl.data.postgres.entities.SavedOrderEntity;
import com.safaricom.dxl.data.postgres.entities.SavedOrderItemEntity;
import com.safaricom.dxl.data.postgres.repositories.SavedOrderItemRepository;
import com.safaricom.dxl.data.postgres.repositories.SavedOrderRepository;
import com.safaricom.dxl.service.SavedOrderService;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.generateId;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;

/**
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SavedOrderServiceImpl implements SavedOrderService {

    private final WsResponseMapper responseMapper;
    private final InputValidation validate;
    private final Shared shared;
    private final SSOToken ssoToken;
    private final SavedOrderRepository savedOrderRepository;
    private final SavedOrderItemRepository savedOrderItemRepository;
    @Lazy
    private final MsConfigProperties properties;


    @Override
    public Mono<WsResponse> addSavedOrder(Map<String, String> headers, String dealerCode, SaveOrderItemsDto dto) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "add saved order");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_ADD_SAVED_ORDER, ERR_VALIDATION, "addSavedOrder");
        return shared.executeWithInputAndSessionValidation(validate.savedOrderPayload(headers, dealerCode, dto),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> savedOrderRepository.countByDealerCode(dealerCode)
                        .flatMap(count -> {
                            if (count > properties.getSavedOrderLimit()) {
                                return shared.customResponse(headers, streamData, "Limit for saved order reached.", ERR_BAD_REQUEST, TRANS_ADD_SAVED_ORDER);
                            }
                            final AtomicReference<String> productCodes = new AtomicReference<>("");
                            dto.getOrderItems().forEach(orderDto -> productCodes.set(productCodes.get().concat(orderDto.getProductCode())));
                            final String orderIdentifier = generateId(dealerCode.concat(String.valueOf(dto.getUserId())).concat(productCodes.get()));
                            return savedOrderRepository.findById(orderIdentifier)
                                    .flatMap(savedOrderEntities -> shared.customResponse(headers, streamData, "You already have this order saved", ERR_BAD_REQUEST, TRANS_ADD_SAVED_ORDER))
                                    .switchIfEmpty(Mono.defer(() -> {
                                        final List<SavedOrderItemEntity> savedOrderItemEntities = new ArrayList<>();
                                        dto.getOrderItems().forEach(order -> savedOrderItemEntities.add(SavedOrderItemEntity.of(order, orderIdentifier)));
                                        return savedOrderRepository.save(SavedOrderEntity.of(dto.getUserId(), orderIdentifier, dealerCode))
                                                .flatMap(saved -> savedOrderItemRepository.saveAll(savedOrderItemEntities).collectList().thenReturn(saved))
                                                .flatMap(saved -> {
                                                    shared.streamToKafka(headers, streamData, null, null);
                                                    return responseMapper.setApiResponse(ERR_SUCCESS, saved, TRANS_ADD_SAVED_ORDER, FALSE, headers);
                                                });
                                    }));
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> fetchSavedOrder(Map<String, String> headers, String dealerCode, String userId, int pageNo, int pageSize) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetch saved order");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_SAVED_ORDER, ERR_VALIDATION, "fetchSavedOrder");
        return shared.executeWithInputAndSessionValidation(validate.fetchSavedCartItemsRequest(headers, dealerCode, userId),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> savedOrderRepository.findByDealerCode(dealerCode, pageNo, pageSize)
                        .flatMap(e -> shared.houseKeepingForEachSavedOrderItem(e.toSavedOrder()))
                        .collectList()
                        .zipWith(savedOrderRepository.countByUserId(userId))
                        .map(tuple -> new PaginationList(tuple.getT2(), tuple.getT1()))
                        .flatMap(paginationList -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_FETCH_SAVED_ORDER, FALSE, headers);
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> deleteSavedOrder(Map<String, String> headers, String dealerCode, String orderIdentifier) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "delete saved order");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_DELETE_SAVED_ORDER, ERR_VALIDATION, "deleteSavedOrder");
        return shared.executeWithInputAndSessionValidation(validate.fetchSavedOrderRequest(headers, dealerCode, orderIdentifier),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> savedOrderRepository.deleteById(orderIdentifier)
                        .thenReturn(new Delete(true))
                        .flatMap(delete -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, delete, TRANS_DELETE_SAVED_ORDER, FALSE, headers);
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> fetchSavedOrderByOrderIdentifier(Map<String, String> headers, String dealerCode, String orderIdentifier) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Fetch saved order by order identifier");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_SAVED_ORDER, ERR_VALIDATION, "fetchSavedOrderByOrderIdentifier");
        return shared.executeWithInputAndSessionValidation(validate.fetchSavedOrderRequest(headers, dealerCode, orderIdentifier),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> savedOrderRepository.findById(orderIdentifier)
                        .zipWith(savedOrderItemRepository.customByOrderIdentifierId(orderIdentifier)
                                .collectList())
                        .flatMap(tuple -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, tuple.getT1().toSavedOrderItems(tuple.getT2()), TRANS_FETCH_SAVED_ORDER, FALSE, headers);
                        })
                        .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, streamData, "No saved order found", ERR_VALIDATION, TRANS_FETCH_SAVED_ORDER))),
                context
        );
    }
}