package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.model.PartnerInfo;
import com.safaricom.dxl.data.model.ValidationContext;
import com.safaricom.dxl.data.postgres.repositories.MiniStoreRepository;
import com.safaricom.dxl.service.MiniStoreService;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.NULL;
import static java.lang.Boolean.FALSE;

@Service
@AllArgsConstructor
public class MiniStoreServiceImpl implements MiniStoreService {
    private final WsResponseMapper responseMapper;
    private final SSOToken ssoToken;
    private final Shared shared;
    private final MiniStoreRepository miniStoreRepository;
    private final InputValidation validate;


    @Override
    public Mono<WsResponse> fetchAllMiniStores(Map<String, String> headers, String dealerCode) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Get All MiniStores");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_MINISTORES, ERR_BAD_REQUEST, "getAllMiniStores");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserName(headers, dealerCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> miniStoreRepository.findAll()
                        .filter(miniStore -> miniStore.getMiniStoreId() != 252)
                        .collectList()
                        .flatMap(miniStoreEntities -> {
                            shared.streamToKafka(headers, partnerInfo, null, null);
                            if (miniStoreEntities.isEmpty()) {
                                return responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_FETCH_MINISTORES, FALSE, headers);
                            }
                            return responseMapper.setApiResponse(ERR_SUCCESS, miniStoreEntities, TRANS_FETCH_MINISTORES, FALSE, headers);
                        }), context
        );
    }

}
