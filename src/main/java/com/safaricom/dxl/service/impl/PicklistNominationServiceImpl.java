package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.config.PIIDataDecryption;
import com.safaricom.dxl.data.dto.CollectorInfoDto;
import com.safaricom.dxl.data.dto.PicklistNominationDto;
import com.safaricom.dxl.data.model.PartnerInfo;
import com.safaricom.dxl.data.model.ValidationContext;
import com.safaricom.dxl.data.postgres.entities.PicklistNominationEntity;
import com.safaricom.dxl.data.postgres.repositories.PicklistNominationRepository;
import com.safaricom.dxl.service.PicklistNominationService;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.stripString;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;
import static java.lang.Boolean.FALSE;

@Service
@AllArgsConstructor
@Slf4j
public class PicklistNominationServiceImpl implements PicklistNominationService {

    private final InputValidation validate;
    private final Shared shared;
    private final SSOToken ssoToken;
    private final PicklistNominationRepository picklistNominationRepository;
    private final WsResponseMapper responseMapper;
    private final PIIDataDecryption piiDataDecryption;


    @Override
    public Mono<WsResponse> addNominee(Map<String, String> headers, PicklistNominationDto nominee, String dealerCode) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Add new Nominee");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_ADD_NOMINEE, ERR_BAD_REQUEST, "addNominee");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserName(headers, dealerCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> picklistNominationRepository.findByDocumentNumberAndMiniStoreName(nominee.getDocumentNumber(), nominee.getMiniStoreName())
                        .flatMap(existingNominee -> {
                            if ("ACTIVE".equalsIgnoreCase(existingNominee.getStatusFlag())) {
                                return shared.customResponse(headers, streamData, "Duplicate ACTIVE record exists", ERR_BAD_REQUEST, TRANS_ADD_NOMINEE);
                            }
                            return addNewNominee(headers, nominee, streamData);
                        })
                        .switchIfEmpty(addNewNominee(headers, nominee, streamData)), context
        );
    }


    @Override
    public Mono<WsResponse> getActiveNominations(Map<String, String> headers, String dealerCode, int page, int size) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Fetch Active Nominations");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_NOMINEE, ERR_BAD_REQUEST, "getActiveNominations");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserName(headers, dealerCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> picklistNominationRepository.findActiveNominationsByDealerCode(dealerCode, PageRequest.of(page, size))
                        .map(nomination -> {
                            String collectorName = nomination.getFirstName() + " " + (nomination.getMiddleName() != null ? nomination.getMiddleName() + " " : "") + nomination.getLastName();
                            return new CollectorInfoDto(nomination.getId(), collectorName, nomination.getMiniStoreName(), nomination.getCreatedBy(), nomination.getCreated());
                        })
                        .collectList()
                        .flatMap(nominations -> {
                            if (nominations == null || nominations.isEmpty()) {
                                streamData.setReportName("No active nominations found");
                                shared.streamToKafka(headers, streamData, null, null);
                                return shared.customResponse(headers, streamData, "No active nominations found.", ERR_NOT_FOUND, TRANS_FETCH_NOMINEE);
                            }
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, nominations, TRANS_FETCH_NOMINEE, FALSE, headers);
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> retireNomination(Map<String, String> headers, Long nominationId, String dealerCode) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Retire Nominee");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_RETIRE_NOMINEE, ERR_BAD_REQUEST, "retireNomination");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserName(headers, dealerCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> picklistNominationRepository.findById(Objects.requireNonNull(nominationId, "Nomination ID cannot be null"))
                        .flatMap(nomination -> {
                            if ("RETIRED".equalsIgnoreCase(nomination.getStatusFlag())) {
                                partnerInfo.setReportName("Nominee has been Retired");
                                shared.streamToKafka(headers, partnerInfo, null, null);
                                return shared.customResponse(headers, partnerInfo, "Nominee has been Retired", ERR_BAD_REQUEST, TRANS_RETIRE_NOMINEE);
                            }
                            nomination.setStatusFlag("RETIRED");
                            nomination.setUpdated(LocalDateTime.now());
                            nomination.setUpdatedBy(headers.get(X_IDENTITY));

                            return picklistNominationRepository.save(nomination)
                                    .flatMap(savedNominee -> {
                                        partnerInfo.setReportName("Nominee retired successfully");
                                        shared.streamToKafka(headers, partnerInfo, null, null);
                                        return responseMapper.setApiResponse(ERR_SUCCESS, savedNominee.toBalance(), TRANS_RETIRE_NOMINEE, FALSE, headers);
                                    });
                        })
                        .switchIfEmpty(Mono.defer(() -> {
                                    partnerInfo.setReportName("Nomination not found");
                                    shared.streamToKafka(headers, partnerInfo, null, null);
                                    return shared.customResponse(headers, partnerInfo, "Nomination ID does not exist", ERR_NOT_FOUND, TRANS_RETIRE_NOMINEE);
                                }
                        )), context
        );

    }

    private Mono<WsResponse> addNewNominee(Map<String, String> headers, PicklistNominationDto nominee, PartnerInfo partnerInfo) {
        PicklistNominationEntity entity = getPicklistNominationEntity(headers, nominee);
        return picklistNominationRepository.save(entity)
                .flatMap(savedNominee -> {
                    savedNominee.setDocumentNumber(piiDataDecryption.decrypt(savedNominee.getDocumentNumber()));
                    savedNominee.setPhoneNumber(piiDataDecryption.decrypt(savedNominee.getPhoneNumber()));
                    partnerInfo.setPhone(savedNominee.getPhoneNumber());
                    return this.generateMessage(savedNominee)
                            .flatMap(message -> shared.sendSmsDxl(headers, stripString(partnerInfo.getPhone()), message)
                                    .doOnSuccess(callback -> WsLogManager.logger.info("SMS Sent to Nominee Successfully"))
                                    .onErrorResume(err -> shared.customResponse(headers, err.getMessage(), "500", TRANS_SEND_SMS))
                                    .then(Mono.defer(() -> {
                                        shared.streamToKafka(headers, partnerInfo, null, null);
                                        return responseMapper.setApiResponse(ERR_SUCCESS, savedNominee.toBalance(), TRANS_ADD_NOMINEE, FALSE, headers);
                                    }))
                            );
                });
    }

    private PicklistNominationEntity getPicklistNominationEntity(Map<String, String> headers, PicklistNominationDto nominee) {
        return new PicklistNominationEntity(
                null,
                piiDataDecryption.encrypt(nominee.getDocumentNumber()),
                nominee.getFirstName(),
                nominee.getMiddleName(),
                nominee.getLastName(),
                nominee.getMiniStoreName(),
                nominee.getDocumentType(),
                piiDataDecryption.encrypt(nominee.getPhoneNumber()),
                nominee.getDealerCode(),
                LocalDateTime.now(),
                headers.get(X_IDENTITY),
                LocalDateTime.now(),
                headers.get(X_IDENTITY),
                "ACTIVE"
        );
    }

    private Mono<String> generateMessage(PicklistNominationEntity savedNominee) {
        String message = String.format("Dear %s %s, you have been authorized to collect stock from %s.", savedNominee.getFirstName(), savedNominee.getLastName(), savedNominee.getMiniStoreName());
        return Mono.just(message);
    }

}
