package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.s3.S3Properties;
import com.safaricom.dxl.data.dto.CartItemsDto;
import com.safaricom.dxl.data.model.*;
import com.safaricom.dxl.data.postgres.entities.CartEntity;
import com.safaricom.dxl.data.postgres.entities.OrderEntity;
import com.safaricom.dxl.data.postgres.entities.OrderItemsEntity;
import com.safaricom.dxl.data.postgres.repositories.*;
import com.safaricom.dxl.service.OrderService;
import com.safaricom.dxl.utils.*;
import com.safaricom.dxl.webflux.starter.logging.WsLogManager;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

import static com.safaricom.dxl.data.enums.OrderItemStatus.*;
import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterDebug;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.REQUEST_FAILED;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static java.lang.Boolean.FALSE;

/**
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final WsResponseMapper responseMapper;
    private final InputValidation validate;
    private final Shared shared;
    private final SSOToken ssoToken;
    @Lazy
    private final CartRepository cartRepository;
    private final OrderRepository orderRepository;
    private final ExcelService excelService;
    private final S3Properties s3Properties;
    private final MsConfigProperties msConfigProperties;
    private final StockRepository stockRepository;
    private final MyWebClient client;
    private final ERPOrder erpOrder;
    private final OrderItemsRepositoryPort orderItemsRepositoryPort;
    private final OrderItemsRepository orderItemsRepository;
    private final TransactionalOperator transactionalOperator;

    @Override
    public Mono<WsResponse> createOrder(Map<String, String> headers, String dealerCode, CartItemsDto dto) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Create Order");
        starterDebug(headers.get(X_CONVERSATION_ID), "createOrder", "", "CartItemsDto: ".concat(dto.toString()));
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_ADD_ORDER, ERR_BAD_REQUEST, "createOrder");

        return shared.executeWithInputAndSessionValidation(validate.createOrderDto(headers, dealerCode, dto),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> cartRepository.findAllById(dto.getCartIds())
                        .flatMap(cartEntity -> this.upsertOrderItemForCart(cartEntity, generateId(localDateTime().toString())))
                        .collectList()
                        .flatMap(itemsEntities -> {
                            if (itemsEntities.isEmpty() || itemsEntities.get(0).getId() == null) {
                                streamData.setReportName("order not created");
                                return shared.customResponse(headers, streamData, "No orders CREATED as they were created before.", ERR_BAD_REQUEST, TRANS_ADD_ORDER);
                            } else {
                                streamData.setReportName("order created");
                                return this.saveAndCreateOrder(headers, dealerCode, dto.getUserId(), itemsEntities, streamData, dto.getCartIds());
                            }
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> fetchOrderByDealerCode(Map<String, String> headers, String dealerCode, int pageNo, int pageSize, String from, String to) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchOrderByDealerCode");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_ORDER, ERR_BAD_REQUEST, "fetchOrderByDealerCode");
        return shared.executeWithInputAndSessionValidation(validate.fetchOrderByDealerCodeRequest(headers, dealerCode, pageSize, from, to),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> orderRepository.customByDealerCode(dealerCode, fromFormattedDate(from), toFormattedDate(to), pageNo, pageSize)
                        .flatMap(this::populateOrderWithItems)
                        .collectList()
                        .zipWith(orderRepository.customCountByDealerCode(dealerCode, fromFormattedDate(from), toFormattedDate(to)))
                        .map(tuple -> new PaginationList(tuple.getT2(), tuple.getT1()))
                        .flatMap(paginationList -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_FETCH_ORDER, FALSE, headers);
                        }), context
        );
    }

    @Override
    public Mono<WsResponse> fetchOrderByOrderNumber(Map<String, String> headers, String dealerCode, String orderNumber) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchOrderByOrderNumber");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_ORDER, ERR_BAD_REQUEST, "fetchOrderByOrderNumber");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserNameAndOrderNumber(headers, dealerCode, orderNumber),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> orderRepository.customByErpOrderIdAndDealerCode(orderNumber, dealerCode)
                        .flatMap(this::populateOrderWithItems)
                        .flatMap(orders -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, orders, TRANS_FETCH_ORDER, FALSE, headers);
                        })
                        .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, streamData, "Not Found", ERR_NOT_FOUND, TRANS_FETCH_ORDER))),
                context
        );
    }

    @Override
    public Mono<WsResponse> exportToS3(Map<String, String> headers, String dealerCode, String startDate, String endDate) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "export to s3");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_EXCEL_FILE_FROM_S3, ERR_BAD_REQUEST, "exportToS3");
        return shared.executeWithInputAndSessionValidation(validate.codeAndDate(headers, dealerCode, startDate, endDate),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> excelService.exportExcelToS3(headers, dealerCode, startDate, endDate)
                        .then(Mono.defer(() -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return shared.processFileRequest(headers, dealerCode, startDate, endDate, "Exemption");
                        })), context
        );
    }

    @Override
    public Mono<WsResponse> fetchExcelUrl(Map<String, String> headers, String dealerCode) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetch Excel");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_EXCEL_FILE_FROM_S3, ERR_BAD_REQUEST, "fetchExcelUrl");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserName(headers, dealerCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> excelService.getFileNameAndPath(dealerCode, s3Properties.getOrdersPrefix())
                        .flatMap(fileName -> {
                            shared.streamToKafka(headers, streamData, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, shared.preSignedUrl(fileName), TRANS_FETCH_EXCEL_FILE_FROM_S3, FALSE, headers);
                        }), context
        );
    }

    private Mono<WsResponse> saveAndCreateOrder(Map<String, String> headers, String dealerCode, long userId, List<OrderItemsEntity> orderItemsEntities, PartnerInfo partnerInfo, List<Long> cartIds) {
        // 1) Validate stock reactively for all items and collect any missing product codes
        return Flux.fromIterable(orderItemsEntities)
                .flatMap(item -> stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(item.getMiniStoreId(), item.getProductCode(), item.getQuantity())
                        .hasElement()
                        .flatMap(avail -> avail ? Mono.empty() : Mono.just(item.getProductCode())))
                .collectList()
                .flatMap(missing -> {
                    if (!missing.isEmpty()) {
                        final String outOfStockProduct = missing.get(0);
                        return shared.customResponse(headers, partnerInfo, String.format("Stock insufficient for %s.", outOfStockProduct), ERR_BAD_REQUEST, TRANS_ADD_ORDER);
                    }

                    // 2) Build order metadata
                    final Set<Long> itemIds = new HashSet<>();
                    final List<OrderRequest> orderRequests = new ArrayList<>();
                    final StringBuilder productAndMiniStore = new StringBuilder();
                    for (OrderItemsEntity item : orderItemsEntities) {
                        itemIds.add(item.getId());
                        orderRequests.add(item.toOrderRequest());
                        productAndMiniStore.append(item.getProductCode()).append(",").append(item.getMiniStoreId());
                    }

                    final String orderIdNew = generateId(dealerCode.concat(String.valueOf(userId)).concat(productAndMiniStore.toString()).concat(localDate().toString()));

                    // 3) Enforce idempotency by checking for an existing similar order
                    return orderRepository.findById(orderIdNew)
                            .flatMap(existing -> orderItemsRepository.deleteAllById(itemIds)
                                    .then(shared.customResponse(headers, partnerInfo, "You have already created another similar Order in the same mini-store. Change order details to proceed.", ERR_BAD_REQUEST, TRANS_ADD_ORDER))
                            )
                            .switchIfEmpty(Mono.defer(() -> {
                                final long requestId = milliSeconds();

                                // 4) Call ERP first. Only on success do we persist DB side effects in a single transaction.
                                return erpOrder.create(headers, dealerCode, orderRequests, client, String.valueOf(requestId), msConfigProperties.getCreateOrderSystemRef())
                                        .flatMap(resp -> {
                                            if ("S".equalsIgnoreCase(resp.getResponseCode())) {
                                                final OrderEntity order = OrderEntity.of(itemIds, dealerCode, userId, requestId, orderIdNew);
                                                order.setErpOrderId(resp.getOrderNumber());
                                                return transactionalOperator.transactional(
                                                        // Decrement stock for each order line
                                                        Flux.fromIterable(orderRequests)
                                                                .flatMap(or -> stockRepository
                                                                        .findByMiniStoreIdAndProductCode(or.getMiniStoreId(), or.getProductCode())
                                                                        .switchIfEmpty(Mono.error(new IllegalStateException("Stock missing during commit for product " + or.getProductCode())))
                                                                        .flatMap(stock -> {
                                                                            stock.setQuantity(stock.getQuantity() - or.getQuantity());
                                                                            return stockRepository.save(stock);
                                                                        }))
                                                                .then(orderItemsRepositoryPort.updateOrderId(itemIds, orderIdNew))
                                                                .then(orderItemsRepositoryPort.updateOrderItemsStatus(itemIds, BOOKED))
                                                                .then(cartRepository.deleteAllById(cartIds))
                                                                .then(orderRepository.save(order))
                                                ).flatMap(saved -> {
                                                            shared.streamToKafka(headers, partnerInfo, null, null);
                                                            return this.generateMessage(msConfigProperties.getOrderMessage(), order.getErpOrderId())
                                                                    .flatMap(message -> shared.sendSmsDxl(headers, stripString(partnerInfo.getPhone()), message)
                                                                            .doOnSuccess(callback -> WsLogManager.logger.info("SMS Sent Successfully"))
                                                                            .onErrorResume(err -> shared.customResponse(headers, err.getMessage(), "500", TRANS_SEND_SMS))
                                                                            .then(responseMapper.setApiResponse(ERR_SUCCESS, saved.toOrderDetails(null, null), TRANS_ADD_ORDER, FALSE, headers))
                                                                    );
                                                        }
                                                );
                                            }

                                            // ERP failure: mark items FAILED and return validation error response. No order is created.
                                            return transactionalOperator.transactional(
                                                            orderItemsRepositoryPort.updateOrderItemsStatus(itemIds, FAILED).then()
                                                    )
                                                    .then(Mono.defer(() -> {
                                                        shared.streamToKafka(headers, partnerInfo, "500", resp.getResponseMessage());
                                                        return shared.customResponse(headers, partnerInfo, resp.getResponseMessage(), ERR_BAD_REQUEST, TRANS_ADD_ORDER);
                                                    }));
                                        })
                                        .onErrorResume(err -> transactionalOperator.transactional( // Any unexpected error: mark items as ORDER_ERROR and return ERR_BAD_REQUEST
                                                        orderItemsRepositoryPort.updateOrderItemsStatus(itemIds, ORDER_ERROR).then()
                                                )
                                                .then(Mono.defer(() -> {
                                                    shared.streamToKafka(headers, partnerInfo, "500", REQUEST_FAILED);
                                                    return shared.customResponse(headers, partnerInfo, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_ADD_ORDER);
                                                })));
                            }))
                            .onErrorResume(err -> shared.customResponse(headers, err.getMessage(), "500", TRANS_ADD_ORDER));
                })
                .onErrorResume(err -> shared.customResponse(headers, err.getMessage(), "500", TRANS_ADD_ORDER));
    }

    private Mono<OrderItemsEntity> upsertOrderItemForCart(CartEntity cart, String orderId) {
        return orderItemsRepository
                .findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(cart.getUserId(), cart.getProductCode(), cart.getMiniStoreId(), PENDING)
                .flatMap(savedOrderItem -> {
                    savedOrderItem.setOrderItemStatus(FAILED);
                    return orderItemsRepository.save(savedOrderItem)
                            .thenReturn(new OrderItemsEntity());
                })
                .switchIfEmpty(Mono.defer(() -> {
                    OrderItemsEntity orderItemsEntity = OrderItemsEntity.of(cart, orderId);
                    return orderItemsRepository.save(orderItemsEntity);
                }));
    }

    private Mono<Order> populateOrderWithItems(OrderDetail orderDetail) {
        Order order = orderDetail.toOrderDetails(orderDetail.getFirstName(), orderDetail.getEmail());
        return orderItemsRepository.customFindByOrderItemIds(orderDetail.getOrderItemIds())
                .collectList()
                .flatMap(orderItemResponses -> {
                    order.setOrderList(orderItemResponses);
                    return Mono.just(order);
                });
    }

    private Mono<String> generateMessage(String messageBody, String orderNumber) {
        return Mono.just(String.format(messageBody, orderNumber));
    }
}