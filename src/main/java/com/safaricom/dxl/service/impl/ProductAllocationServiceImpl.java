package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.SingleProductAllocationData;
import com.safaricom.dxl.data.model.AllocationBalance;
import com.safaricom.dxl.data.model.PartnerInfo;
import com.safaricom.dxl.data.model.ValidationContext;
import com.safaricom.dxl.data.mysql.repositories.DpOrderRepository;
import com.safaricom.dxl.data.postgres.entities.ProductAllocationEntity;
import com.safaricom.dxl.data.postgres.repositories.LogsAllocationRepository;
import com.safaricom.dxl.data.postgres.repositories.OrderItemsRepository;
import com.safaricom.dxl.data.postgres.repositories.ProductAllocationRepository;
import com.safaricom.dxl.exception.AllocationConflictException;
import com.safaricom.dxl.service.ProductAllocationService;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.utils.Utilities;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.io.ByteArrayInputStream;
import java.nio.ByteBuffer;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.localDate;
import static com.safaricom.dxl.utils.Utilities.localDateTime;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterInfo;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@AllArgsConstructor
public class ProductAllocationServiceImpl implements ProductAllocationService {

    private final InputValidation validate;
    private final Shared shared;
    private final SSOToken ssoToken;
    private final ProductAllocationRepository productAllocationRepository;
    private final WsResponseMapper responseMapper;
    private final LogsAllocationRepository logsAllocationRepository;
    private final DpOrderRepository dpOrderRepository;
    private final OrderItemsRepository orderItemsRepository;

    @Override
    public Mono<WsResponse> fetchProductAllocationDetailsByDealerCode(Map<String, String> headers, String dealerCode, String productCode) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Fetch Ordering Dealer Allocation");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS, ERR_BAD_REQUEST, "fetchProductAllocationDetailsByDealerCode");
        LocalDate date = localDate();
        return shared.executeWithInputAndSessionValidation(validate.codeAndProductCode(headers, dealerCode, productCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> this.updateOrderedQuantityNew(headers, dealerCode, productCode, date)
                        .flatMap(s -> this.processAllocation(headers, dealerCode, productCode, date, streamData)),
                context);
    }

    private Mono<ProductAllocationEntity> updateOrderedQuantityNew(Map<String, String> headers, String dealerCode, String productCode, LocalDate now) {
        starterInfo(headers.get(X_CONVERSATION_ID), "updateOrderedQuantityNew", "", "LocalDate now: ".concat(now.toString()));
        return productAllocationRepository.findByDealerCodeAndProductCodeAndStartDateLessThanEqualAndEndDateGreaterThanEqual(dealerCode.trim(), productCode.trim(), now, now)
                .flatMap(entity -> {
                    LocalDateTime start = entity.getStartDate().atStartOfDay();
                    LocalDateTime end = entity.getEndDate().atTime(23, 59, 59, 999_999_999);
                    //Get Ordered items from DP and PP.
                    return dpOrderRepository.customByDealerCodeAndProductCodeAndStartDateAndEndDate(dealerCode, productCode, start, end)
                            .zipWith(orderItemsRepository.customByDealerCodeAndProductCodeAndStartDateAndEndDate(dealerCode, productCode, start, end))
                            //Get sum of both.
                            .map(tuple -> (int) (tuple.getT1() + tuple.getT2()))
                            .flatMap(ordered -> {
                                //update entity with new ordered value.
                                entity.setOrdered(ordered);
                                entity.setUpdatedOn(localDateTime());
                                entity.setUpdatedBy(headers.get(X_PARTNER_IDENTITY));
                                //Save total ordered.
                                return productAllocationRepository.save(entity)
                                        .doOnSuccess(s -> starterInfo(headers.get(X_CONVERSATION_ID), "updateOrderedQuantity", "", String.format("Allocation for %s and %s updated with %s quantities.", s.getDealerCode(), s.getProductCode(), s.getOrdered())));
                            });
                }).switchIfEmpty(Mono.defer(() -> {
                    starterInfo(headers.get(X_CONVERSATION_ID), "updateOrderedQuantity", "", String.format("No record found for %s and %s.", dealerCode, productCode));
                    return Mono.just(new ProductAllocationEntity());
                }));
    }

    private Mono<WsResponse> processAllocation(Map<String, String> headers, String dealerCode, String productCode, LocalDate date, PartnerInfo partnerInfo) {
        return productAllocationRepository.findByProductCode(productCode).collectList()
                .flatMap(existingProducts -> {
                    if (!existingProducts.isEmpty()) {
                        return productAllocationRepository.findProductAllocationWithDates(dealerCode, productCode, localDate(), localDate())
                                .flatMap(dealerAllocation -> {
                                    if (date.isBefore(dealerAllocation.getStartDate()) || date.isAfter(dealerAllocation.getEndDate())) {
                                        return shared.customResponse(headers, partnerInfo, "No Allocations Available for today", ERR_VALIDATION, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS);
                                    }
                                    AllocationBalance allocationBalance = dealerAllocation.toBalance();
                                    allocationBalance.setAvailableBalance(dealerAllocation.getAllocation() - dealerAllocation.getOrdered());
                                    return logsAllocationRepository.findByAllocationId(dealerAllocation.getId())
                                            .map(Utilities::getProductAllocationLogData)
                                            .collectList()
                                            .flatMap(logDataList -> {
                                                if (!logDataList.isEmpty()) {
                                                    allocationBalance.setLogData(logDataList);
                                                }
                                                shared.streamToKafka(headers, partnerInfo, null, null);
                                                return responseMapper.setApiResponse(ERR_SUCCESS, allocationBalance, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS, FALSE, headers);
                                            });
                                })
                                .switchIfEmpty(Mono.defer(() -> {
                                    partnerInfo.setReportName("No Dealer Allocation");
                                    return shared.customResponse(headers, partnerInfo, "No Allocations available", ERR_VALIDATION, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS);
                                }))
                                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS));
                    }

                    shared.streamToKafka(headers, partnerInfo, null, "Product Does Not Require Allocation");
                    // If existingProducts is empty
                    return responseMapper.setApiResponse(ERR_SUCCESS, "Product Does Not Require Allocation", TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS, FALSE, headers);
                });
    }

    @Override
    @Transactional
    public Mono<WsResponse> processExcelFile(Map<String, String> headers, FilePart file, LocalDate startDate, LocalDate endDate, String productName, String allocationFor, boolean isUpdate) {

        starterInfo(headers.get(X_CONVERSATION_ID), "processExcelFile", "", headers.get(X_PARTNER_IDENTITY));

        if (!endDate.equals(endDate.withDayOfMonth(endDate.lengthOfMonth()))
                && !endDate.equals(endDate.withDayOfMonth(endDate.lengthOfMonth() - 1))) {
            return Mono.error(new AllocationConflictException("End date must be the last day or second-to-last day of the month"));
        }

        return file.content()
                .flatMapSequential(dataBuffer -> {
                    ByteBuffer byteBuffer = dataBuffer.asByteBuffer();
                    byte[] bytes = new byte[byteBuffer.remaining()];
                    byteBuffer.get(bytes);
                    return Mono.just(new ByteArrayInputStream(bytes));
                })
                .single()
                .flatMap(inputStream -> shared.extractAndUpdateAllocations(headers, inputStream, startDate, endDate, productName, allocationFor, isUpdate))
                .onErrorResume(ex -> shared.customResponse(headers, REQUEST_FAILED, "400", "VALIDATION_ERROR"));
    }

    @Override
    public Mono<WsResponse> allocateSingleProduct(Map<String, String> headers, SingleProductAllocationData data) {

        Mono<ProductAllocationEntity> allocateResponse = shared.updateAllocations(headers, data.getDealerCode(), data.getProductCode(),
                data.getNewAllocation(), data.getStartDate(), data.getEndDate(), data.getProductName(), data.getAllocationFor(), data.getIsUpdate());
        return allocateResponse.flatMap(x -> responseMapper.setApiResponse(ERR_SUCCESS, null, "", FALSE, headers));
    }

    @Override
    public Mono<WsResponse> fetchDealerAllocations(Map<String, String> headers, String dealerCode) {
        PartnerInfo partnerInfo = new PartnerInfo(System.currentTimeMillis(), dealerCode, "Fetch Allocations");
        ValidationContext context = new ValidationContext(headers, partnerInfo, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS, ERR_BAD_REQUEST, "fetchDealerAllocations");
        return shared.executeWithInputAndSessionValidation(validate.codeAndUserName(headers, dealerCode),
                errorMapping -> ssoToken.validateSessionAndCode(headers, dealerCode),
                streamData -> productAllocationRepository.findProductAllocationWithDate(dealerCode, localDate())
                        // for each allocation, fetch logs and map to ProductAllocationData
                        .flatMap(allocation ->
                                logsAllocationRepository.findByAllocationId(allocation.getId())
                                        .collectList()
                                        .map(allocation::toData)
                        )
                        .collectList()
                        .flatMap(allocationDataList -> {
                            if (allocationDataList.isEmpty()) {
                                return shared.customResponse(headers, partnerInfo, "No Allocations Found", ERR_NOT_FOUND, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS);
                            }
                            shared.streamToKafka(headers, partnerInfo, null, null);
                            return responseMapper.setApiResponse(ERR_SUCCESS, allocationDataList, TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS, FALSE, headers);
                        }), context
        );
    }


}