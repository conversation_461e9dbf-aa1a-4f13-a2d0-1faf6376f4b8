package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.SingleProductAllocationData;
import com.safaricom.dxl.service.ProductAllocationService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Map;


@Tag(name = "Product Allocation", description = "Query product allocation details")
@RestController
@RequestMapping("/fetchProductAllocation")
@AllArgsConstructor
public class ProductAllocationController {
    private final ProductAllocationService productAllocationService;

    @Operation(summary = "Fetch product allocation details by dealer code")
    @GetMapping("/fetchProductAllocationByDealerCode")
    public Mono<WsResponse> fetchProductAllocationByDealerCode(
            @RequestHeader Map<String, String> headers,
            @RequestParam(name = "dealerCode") String dealerCode,
            @RequestParam(name = "productCode") String productCode) {
        return productAllocationService.fetchProductAllocationDetailsByDealerCode(headers, dealerCode, productCode);
    }

    @Operation(summary = "Fetch product allocation details by dealer code")
    @GetMapping("/fetchDealerAllocations")
    public Mono<WsResponse> fetchDealerAllocations(
            @RequestHeader Map<String, String> headers,
            @RequestParam(name = "dealerCode") String dealerCode) {
        return productAllocationService.fetchDealerAllocations(headers, dealerCode);
    }

    @Operation(summary = "Upload Excel file")
    @PostMapping(value = "/uploadExcel")
    public Mono<WsResponse> uploadExcelFile(
            @RequestHeader Map<String, String> headers,
            @RequestPart("file") FilePart productAllocationDto,
            @RequestParam("startDate") LocalDate startDate,
            @RequestParam("endDate") LocalDate endDate,
            @RequestParam("product") String productName,
            @RequestParam("allocationFor") String allocationFor,
            @RequestParam("updateFlag") boolean updateFlag) {

        return productAllocationService.processExcelFile(headers, productAllocationDto, startDate, endDate, productName, allocationFor, updateFlag);
    }


    @Operation(summary = "Method for testing product allocation")
    @PostMapping(value = "/single-product") //helper method for quick testing
    public Mono<WsResponse> singleProductAllocation(
            @RequestHeader Map<String, String> headers,
            @RequestBody SingleProductAllocationData data) {

        return productAllocationService.allocateSingleProduct(headers, data);
    }

}
