package com.safaricom.dxl.controller;

import com.safaricom.dxl.service.MiniStoreService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;
/**
 *
 * <AUTHOR>
 */
@Tag(name="MiniStore", description = "Controller")
@RestController
@RequestMapping("/picklist-nomination")
@AllArgsConstructor
@Validated
public class MiniStoreController {

    private MiniStoreService miniStoreService;

    @GetMapping("/ministores")
    public Mono<WsResponse> fetchAllMiniStores(@RequestHeader Map<String, String> headers, @RequestParam String dealerCode) {
        return miniStoreService.fetchAllMiniStores(headers, dealerCode);
    }
}

