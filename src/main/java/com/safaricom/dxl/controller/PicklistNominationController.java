package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.PicklistNominationDto;
import com.safaricom.dxl.service.PicklistNominationService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

@Tag(name="PicklistNomination", description = "Controller")
@RestController
@RequestMapping("/picklist-nomination")
@AllArgsConstructor
@Slf4j
@Validated
public class PicklistNominationController {

    private final PicklistNominationService nominationService;

    @PostMapping("/add")
    public Mono<WsResponse> addNominee(@RequestHeader Map<String, String> headers,
                                       @Valid @RequestBody PicklistNominationDto nominee,
                                       @RequestParam String dealerCode) {
        return nominationService.addNominee(headers, nominee, dealerCode);
    }


    @GetMapping("/active")
    public Mono<WsResponse> getActiveNominations(
            @RequestHeader Map<String, String> headers,
            @RequestParam String dealerCode,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") @Valid @Max(value = 200, message = "please present a valid size") int size) {
        return nominationService.getActiveNominations(headers, dealerCode, page, size);
    }

    @DeleteMapping("/retire")
    public Mono<WsResponse> retireNomination(@RequestHeader Map<String, String> headers,
                                             @RequestParam Long id,
                                             @RequestParam String dealerCode) {
        return nominationService.retireNomination(headers, id, dealerCode);
    }

}
