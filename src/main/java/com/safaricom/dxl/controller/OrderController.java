package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.CartItemsDto;
import com.safaricom.dxl.service.OrderService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */

@Tag(name="Ms Starter", description = "Controller")
@RestController
@RequestMapping("/order")
@AllArgsConstructor
@Validated
public class OrderController {

    private final OrderService orderService;
    
    @Operation(summary = "Create Order")
    @PostMapping("/")
    public Mono<WsResponse> createOrder(@RequestHeader Map<String, String> headers,
                                        @RequestParam(name = "dealerCode") String dealerCode,
                                        @Valid @RequestBody CartItemsDto cartIds){
        return orderService.createOrder(headers, dealerCode, cartIds);
    }

    @Operation(summary = "Fetch order by dealer code")
    @GetMapping("/fetchOrderByDealerCode")
    public Mono<WsResponse> fetchOrderByDealerCode(@RequestHeader Map<String, String> headers,
                                                   @RequestParam(name = "dealerCode") String dealerCode,
                                                   @RequestParam(defaultValue = "0", required = false) int pageNo,
                                                   @RequestParam(defaultValue = "10", required = false) int pageSize,
                                                   @RequestParam(name = "from", required = false) String from,
                                                   @RequestParam(name = "to", required = false) String to) {
        return orderService.fetchOrderByDealerCode(headers, dealerCode, pageNo, pageSize, from, to);
    }

    @Operation(summary = "Fetch order by Order Number")
    @GetMapping("/")
    public Mono<WsResponse> fetchOrderByOrderNumber(@RequestHeader Map<String, String> headers,
                                                    @RequestParam(name = "dealerCode") String dealerCode,
                                                    @RequestParam(name = "orderNumber") String orderNumber) {
        return orderService.fetchOrderByOrderNumber(headers, dealerCode, orderNumber);
    }

    @Operation(summary = "Get starter response")
    @GetMapping("/exportToS3")
    public Mono<WsResponse> exportToS3(@RequestHeader Map<String, String> headers,
                                       @RequestParam(name = "dealerCode") String shortCode,
                                       @RequestParam(name = "startDate") String startDate,
                                       @RequestParam(name = "endDate") String endDate){
        return orderService.exportToS3(headers, shortCode, startDate, endDate);
    }

    @Operation(summary = "Get starter response")
    @GetMapping("/getExcelUrl")
    public Mono<WsResponse> fetchExcelUrl(@RequestHeader Map<String, String> headers, @RequestParam(name = "shortCode") String shortCode){
        return orderService.fetchExcelUrl(headers, shortCode);
    }

}