package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.SaveOrderItemsDto;
import com.safaricom.dxl.service.SavedOrderService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Tag(name="Ms Starter", description = "Controller")
@RestController
@RequestMapping("/saved-order")
@AllArgsConstructor
@Validated
public class SavedOrderController {

    private final SavedOrderService savedOrderService;

    @Operation(summary = "Create Saved order")
    @PostMapping("/")
    public Mono<WsResponse> add(@RequestHeader Map<String, String> headers,
                                @RequestParam(name = "dealerCode") String dealerCode,
                                @Valid @RequestBody SaveOrderItemsDto payload){
        return savedOrderService.add(headers, dealerCode, payload);
    }

    @Operation(summary = "Get Saved order")
    @GetMapping("/")
    public Mono<WsResponse> fetchSavedOrder(@RequestHeader Map<String, String> headers,
                                            @RequestParam(name = "dealerCode") String dealerCode,
                                            @RequestParam(name = "userId") String userId,
                                            @RequestParam(defaultValue = "0", required = false) int pageNo,
                                            @RequestParam(defaultValue = "10", required = false) int pageSize){
        return savedOrderService.fetchSavedOrder(headers, dealerCode, userId, pageNo, pageSize);
    }

    @Operation(summary = "Get Saved order")
    @GetMapping("/fetchByOrderIdentifier")
    public Mono<WsResponse> fetchSavedOrderByOrderIdentifier(@RequestHeader Map<String, String> headers,
                                            @RequestParam(name = "dealerCode") String dealerCode,
                                            @RequestParam(name = "orderIdentifier") String orderIdentifier){
        return savedOrderService.fetchSavedOrderByOrderIdentifier(headers, dealerCode, orderIdentifier);
    }

    @Operation(summary = "Delete Saved order")
    @DeleteMapping("/")
    public Mono<WsResponse> deleteSavedOrder(@RequestHeader Map<String, String> headers,
                                                 @RequestParam(name = "dealerCode") String dealerCode,
                                                 @RequestParam(name = "orderIdentifier") String orderIdentifier){
        return savedOrderService.deleteSavedOrder(headers, dealerCode, orderIdentifier);
    }
}