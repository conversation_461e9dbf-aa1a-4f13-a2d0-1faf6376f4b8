package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.CartDto;
import com.safaricom.dxl.service.CartService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Tag(name="Ms Starter", description = "Controller")
@RestController
@RequestMapping("/cart")
@AllArgsConstructor
@Validated
public class CartController {

    private final CartService cartService;
    
    @Operation(summary = "Create Cart")
    @PostMapping("/")
    public Mono<WsResponse> add(@RequestHeader Map<String, String> headers, @Valid @RequestBody CartDto payload) {
        return cartService.add(headers, payload);
    }

    @Operation(summary = "Get Cart Items")
    @GetMapping("/")
    public Mono<WsResponse> fetchCartItems(@RequestHeader Map<String, String> headers,
                                           @RequestParam(name = "dealerCode") String dealerCode,
                                           @RequestParam(name = "userId") String userId){
        return cartService.fetchCartItems(headers, dealerCode, userId);
    }

    @Operation(summary = "Delete Saved Cart Items")
    @DeleteMapping("/")
    public Mono<WsResponse> deleteSavedCartItems(@RequestHeader Map<String, String> headers,
                                                 @RequestParam(name = "dealerCode") String dealerCode,
                                                 @RequestParam(name = "cartId") String cartId){
        return cartService.deleteSavedCartItems(headers, dealerCode, cartId);
    }

}