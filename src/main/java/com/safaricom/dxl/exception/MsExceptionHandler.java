package com.safaricom.dxl.exception;

import com.safaricom.dxl.webflux.starter.exception.WsExceptionHandler;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import com.safaricom.dxl.webflux.starter.utils.WsStarterVariables;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
public class MsExceptionHandler extends WsExceptionHandler {

    public MsExceptionHandler(WsMappingService mappingService) {
        super(mappingService);
    }

    @ExceptionHandler({ProductCodeNotFoundException.class})
    protected Mono<ResponseEntity<WsResponse>> handleProductCodeNotFoundError(ProductCodeNotFoundException ex, ServerHttpRequest request) {
        return this.setErrResponse(ex.getCode(), ex.getMessage(), WsStarterVariables.NULL, request.getHeaders(), HttpStatus.BAD_REQUEST, false);
    }

    @ExceptionHandler({ExcelDataValidationException.class})
    protected Mono<ResponseEntity<WsResponse>> handleExcelDataValidationError(ExcelDataValidationException ex, ServerHttpRequest request) {
        return this.setErrResponse(ex.getCode(), ex.getMessage(), WsStarterVariables.NULL, request.getHeaders(), HttpStatus.BAD_REQUEST, false);
    }
}