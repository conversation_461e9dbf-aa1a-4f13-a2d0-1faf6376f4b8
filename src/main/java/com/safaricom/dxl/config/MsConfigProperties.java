package com.safaricom.dxl.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "dxl.ms")
public class MsConfigProperties {
    private String orderMessage;
    private String key;
    private String initializationVector;
    private String localDateRegex;
    private int ttl;
    private String roleNotAllowedToQueryOtherDealers;
    private boolean enableStreaming;
    private boolean enableSessionAndCodeValidation;

    private String createOrderUrl;
    private String balanceUrl;
    private String stockCountUrl;
    private String validateOrderUrl;
    private String sendSmsUrl;
    private String sendSmsAuth;

    private int allowedOrderItems;
    private String createOrderSystemRef;
    private String identityValidation;
    private int savedOrderLimit;
    private String orderQuantityCountUrl;
    /**
     * Endpoint URL for DXL integration
     */
    private String dxlEndpoint;
    private String dxlAuth;
    private String dxlApiKey;
    private String smsSenderId;
}