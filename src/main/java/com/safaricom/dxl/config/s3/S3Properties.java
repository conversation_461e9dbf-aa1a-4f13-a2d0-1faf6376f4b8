package com.safaricom.dxl.config.s3;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dxl.s3")
public class S3Properties {
    private String accessId;
    private String accessToken;
    private String region;
    private String bucket;
    private String bucketUrl;
    private String ordersPrefix;
    private Long signatureExpiry = 300L; //in seconds
}
