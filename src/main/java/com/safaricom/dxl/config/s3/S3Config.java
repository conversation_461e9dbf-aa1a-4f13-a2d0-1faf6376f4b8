package com.safaricom.dxl.config.s3;

import com.safaricom.dxl.config.PIIDataDecryption;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;


/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Configuration
public class S3Config {
    private final PIIDataDecryption piiDataDecryption;

    @Bean
    public S3Client s3Client(S3Properties s3Properties) {
        return S3Client.builder()
                .region(Region.of(s3Properties.getRegion()))
                .credentialsProvider(getAwsCredentialsProvider(s3Properties))
                .build();
    }

    @Bean
    public S3Presigner s3Presigner(S3Properties properties) {
        return S3Presigner.builder()
                .region(Region.of(properties.getRegion()))
                .credentialsProvider(getAwsCredentialsProvider(properties))
                .build();
    }

    private AwsCredentialsProvider getAwsCredentialsProvider(S3Properties properties) {
        return StaticCredentialsProvider
                .create(AwsBasicCredentials.create(piiDataDecryption.decrypt(properties.getAccessId()),
                        piiDataDecryption.decrypt(properties.getAccessToken())));
    }
}