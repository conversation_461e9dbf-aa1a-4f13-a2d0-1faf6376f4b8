package com.safaricom.dxl.config;

import io.r2dbc.spi.ConnectionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.r2dbc.connection.R2dbcTransactionManager;
import org.springframework.transaction.ReactiveTransactionManager;
import org.springframework.transaction.reactive.TransactionalOperator;

/**
 * Reactive transaction configuration providing TransactionalOperator
 * for composing multi-step R2DBC operations atomically.
 */
@Configuration
public class ReactiveTxConfig {

    @Bean
    public ReactiveTransactionManager reactiveTransactionManager(final ConnectionFactory connectionFactory) {
        return new R2dbcTransactionManager(connectionFactory);
    }

    @Bean
    public TransactionalOperator transactionalOperator(final ReactiveTransactionManager transactionManager) {
        return TransactionalOperator.create(transactionManager);
    }
}
