package com.safaricom.dxl.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "dxl.di")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MsDIProperties {
    private String xMessageId;

    private String validateTokenStsUrl;

    private boolean ssoEnabled;
    private int ttl;
    //DI stand for Digital Identity
    private String partnerSourceSystem;
    private String sourceSystem;
    private String basicAuth;
}
