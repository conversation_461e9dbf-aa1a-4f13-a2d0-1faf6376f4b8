package com.safaricom.dxl.config;

import com.safaricom.dxl.encryption.security.Decryption;
import com.safaricom.dxl.encryption.security.Encryption;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PIIDataDecryption extends Decryption {
    private final MsConfigProperties properties;

    public String decrypt(String text){
        return decryptInternalRequest(properties.getKey(),properties.getInitializationVector(),  text);
    }

    public String encrypt(String text){
        Encryption encryption = new Encryption();
        return encryption.encryptInternalMsisdnRequest(properties.getKey(),properties.getInitializationVector(),  text);
    }
}
