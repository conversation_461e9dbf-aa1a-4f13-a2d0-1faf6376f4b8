package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.s3.S3Properties;
import com.safaricom.dxl.data.postgres.entities.OrderEntity;
import com.safaricom.dxl.data.postgres.entities.S3FileExportEntity;
import com.safaricom.dxl.data.postgres.repositories.OrderRepository;
import com.safaricom.dxl.data.postgres.repositories.S3FileExportRepository;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

@Service
@RequiredArgsConstructor
public class ExcelService {
    private final Shared shared;
    private final S3Properties properties;
    private final OrderRepository orderRepository;
    private final S3FileExportRepository s3FileExportRepository;

    private void createHeader(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("User Id");
        headerRow.createCell(1).setCellValue("Order Id");
        headerRow.createCell(2).setCellValue("Dealer Code");
        headerRow.createCell(3).setCellValue("Order Status");
        headerRow.createCell(4).setCellValue("Invoice Number");
        headerRow.createCell(5).setCellValue("CU Number");
        headerRow.createCell(6).setCellValue("Created");
    }

    private void writeEntity(Sheet sheet, OrderEntity entity) {
        int rowIndex = sheet.getPhysicalNumberOfRows();
        Row dataRow = sheet.createRow(rowIndex);
        dataRow.createCell(0).setCellValue(entity.getUserId());
        dataRow.createCell(1).setCellValue(entity.getErpOrderId());
        dataRow.createCell(2).setCellValue(entity.getDealerCode());
        dataRow.createCell(3).setCellValue(entity.getOrderStatus().value);
        dataRow.createCell(4).setCellValue(entity.getCuNumber());
        dataRow.createCell(5).setCellValue(String.valueOf(entity.getCreated()));
    }

    public Mono<Void> exportExcelToS3(final Map<String, String> headers, final String dealerCode, final String startDate, final String endDate) {
        return Mono.using(
                        // Resource supplier: create workbook
                        () -> {
                            final SXSSFWorkbook workbook = new SXSSFWorkbook();
                            workbook.setCompressTempFiles(true);
                            return workbook;
                        },
                        workbook -> Mono.using(
                                // Resource supplier: create output stream
                                ByteArrayOutputStream::new,
                                outputStream -> {
                                    final String fileName = getFileName(dealerCode, properties.getOrdersPrefix());
                                    final Sheet sheet = workbook.createSheet(getSheetName(dealerCode, startDate, endDate));
                                    createHeader(sheet);

                                    return orderRepository.findByDealerCodeAndCreatedBetweenOrderByCreatedDesc(dealerCode, fromFormattedDate(startDate), toFormattedDate(endDate))
                                            .doOnNext(entity -> writeEntity(sheet, entity))
                                            .then(Mono.fromCallable(() -> {
                                                workbook.write(outputStream);
                                                return outputStream.toByteArray();
                                            }))
                                            .flatMap(bytes -> shared.upload(bytes, fileName, properties.getOrdersPrefix(), "xlsx"))
                                            .flatMap(file -> s3FileExportRepository.save(S3FileExportEntity.of(dealerCode, startDate, endDate, "Orders")).thenReturn(file))
                                            .then();
                                },
                                // OutputStream cleanup
                                outputStream -> {
                                    try {
                                        outputStream.close(); // blocking call to be handled in boundedElastic scheduler
                                    } catch (IOException e) {
                                        starterError(headers.get(X_CONVERSATION_ID), TRANS_EXCEL_CREATION, "", e.getMessage());
                                    }
                                }
                        ),
                        // Workbook cleanup
                        workbook -> {
                            try {
                                workbook.close();
                            } catch (IOException e) {
                                starterError(headers.get(X_CONVERSATION_ID), TRANS_EXCEL_CREATION, "", e.getMessage());
                            }
                        }
                )
                .onErrorMap(e -> {
                    starterError(headers.get(X_CONVERSATION_ID), TRANS_EXCEL_CREATION, "", e.getMessage());
                    return e;
                })
                .subscribeOn(Schedulers.boundedElastic());
    }


    public Mono<String> getFileNameAndPath(String shortCode, String prefix) {
        return Mono.just(String.format(FILE_PATH_NAME_FORMAT, prefix.replace("/", ""), getFileName(shortCode, prefix)));
    }

    private String getFileName(String shortCode, String prefix) {
        return String.format(FILENAME_FORMAT, prefix.replace("/", ""), generateId(shortCode.toUpperCase()), XLSX_SUFFIX);
    }

    private String getSheetName(String shortCode, String startDate, String endDate) {
        return String.format(SHEET_NAME_FORMAT, shortCode.toUpperCase(), startDate, endDate);
    }
}
