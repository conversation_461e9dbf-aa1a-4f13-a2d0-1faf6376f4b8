package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.s3.S3Properties;
import com.safaricom.dxl.data.dto.ExcelData;
import com.safaricom.dxl.data.model.*;
import com.safaricom.dxl.data.postgres.entities.*;
import com.safaricom.dxl.data.postgres.repositories.*;
import com.safaricom.dxl.data.response.SavedOrderResponse;
import com.safaricom.dxl.exception.AllocationConflictException;
import com.safaricom.dxl.exception.ExcelDataValidationException;
import com.safaricom.dxl.exception.ProductCodeNotFoundException;
import com.safaricom.dxl.service.impl.KafkaStreamingService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.model.WsResponseDetails;
import com.safaricom.dxl.webflux.starter.model.WsTransactionalDetails;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetUrlRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.InputStream;
import java.net.URL;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterInfo;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@AllArgsConstructor
public class Shared {
    private final ProductCategoryRepository productCategoryRepository;
    private final ProductRepository productRepository;
    private final WsResponseMapper responseMapper;
    private final MsConfigProperties msConfigProperties;
    private final KafkaStreamingService kafkaStreamingService;
    private final MyWebClient client;
    private final S3Properties s3Props;
    private final S3Client s3Client;
    private final S3Presigner preSigner;
    private final S3FileExportRepository s3FileExportRepository;
    private final SavedOrderItemRepository savedOrderItemRepository;
    private final ProductAllocationRepository productAllocationRepository;
    private final LogsAllocationRepository logsAllocationRepository;
    private WsMappingService wsMappingService;


    public WsResponseDetails details(Map<String, String> headers, String customerMessage, String techMessage, String errCode, String processParam) {
        WsResponseDetails responseDetails = wsMappingService.getErrorMapper(errCode, headers.get(X_CONVERSATION_ID), processParam);
        responseDetails.setCode(errCode);
        responseDetails.setCustomerMessage(customerMessage);
        responseDetails.setTechnicalMessage(techMessage);
        return responseDetails;
    }

    public Mono<WsResponse> customResponse(Map<String, String> headers, String message, String code, String transactionType) {
        WsResponseDetails responseDetails = details(headers, message, message, code, transactionType);
        return responseMapper.setApiResponse(responseDetails, NULL, transactionType, new WsTransactionalDetails(), NULL, NULL, ES, FALSE, headers);
    }

    public Mono<WsResponse> customResponse(Map<String, String> headers, PartnerInfo partnerInfo, String message, String code, String transactionType) {
        streamToKafka(headers, partnerInfo, code, message);
        WsResponseDetails responseDetails = details(headers, message, message, code, transactionType);
        return responseMapper.setApiResponse(responseDetails, NULL, transactionType, new WsTransactionalDetails(), NULL, NULL, ES, FALSE, headers);
    }

    public void streamToKafka(Map<String, String> headers, PartnerInfo partnerInfo, String errorCode, String errorMessage) {
        if (msConfigProperties.isEnableStreaming()) {
            DataStreaming dataStreaming = new DataStreaming();
            dataStreaming.setSourceSystem(headers.get(X_SOURCE_SYSTEM));
            dataStreaming.setServiceName(SERVICE_NAME);
            dataStreaming.setResource(partnerInfo.getReportName());
            dataStreaming.setDealerCode(partnerInfo.getDealerCode());
            dataStreaming.setDealerName(partnerInfo.getDealerName());
            dataStreaming.setRole(stripString(partnerInfo.getRole()));
            dataStreaming.setTimeStamp(partnerInfo.getStartTime());
            dataStreaming.setTransactionId(headers.get(X_CONVERSATION_ID));
            dataStreaming.setResponseCode(errorCode == null ? ERR_SUCCESS : errorCode);
            dataStreaming.setResponseMessage(errorMessage == null ? "success" : errorMessage);
            dataStreaming.setTransactional(partnerInfo.getTransactionValue() != null && !partnerInfo.getTransactionValue().isNaN());
            dataStreaming.setTransactionValue(partnerInfo.getTransactionValue() != null && !partnerInfo.getTransactionValue().isNaN() ? partnerInfo.getTransactionValue() : 0L);
            dataStreaming.setResponseTime(System.currentTimeMillis() - partnerInfo.getStartTime());
            dataStreaming.setExtraInfo(partnerInfo.getAdditionalData());
            dataStreaming.setUsername(stripString(partnerInfo.getEmail()));
            kafkaStreamingService.streamToKafka(dataStreaming, partnerInfo.getStartTime());
        }
    }

    public Mono<WsResponse> sendSmsDxl(Map<String, String> headers, String msisdn, String message) {
        return client.sendDxlSms(headers)
                .post()
                .body(BodyInserters.fromValue(getDxlSmsBody(msisdn, message, msConfigProperties)))
                .retrieve()
                .bodyToMono(WsResponse.class)
                .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                        .filter(Utilities::shouldRetry));
    }

    public Mono<FileDetails> upload(byte[] file, String imageName, String prefix, String contentType) {
        PutObjectRequest objectRequest = PutObjectRequest.builder().bucket(s3Props.getBucket()).key(prefix.concat(imageName)).contentType(contentType).build();
        s3Client.putObject(objectRequest, RequestBody.fromBytes(file));
        var fileUrl = s3Client.utilities().getUrl(GetUrlRequest.builder().bucket(s3Props.getBucket()).key(imageName).build()).toExternalForm();
        return Mono.just(new FileDetails(imageName, fileUrl));
    }

    public URL preSignedUrl(String imageName) {

        GetObjectRequest getObjectRequest = GetObjectRequest.builder().bucket(s3Props.getBucket()).key(imageName).build();

        GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder().signatureDuration(Duration.ofSeconds(s3Props.getSignatureExpiry())).getObjectRequest(getObjectRequest).build();

        PresignedGetObjectRequest presignedGetObjectRequest = preSigner.presignGetObject(getObjectPresignRequest);
        return presignedGetObjectRequest.url();
    }

    public Mono<WsResponse> processFileRequest(Map<String, String> headers, String shortCode, String startDate, String endDate, String reportName) {
        String reportId = generateId(shortCode + startDate + endDate + reportName);
        return s3FileExportRepository.findById(reportId).flatMap(s3FileExportEntity -> responseMapper.setApiResponse(ERR_SUCCESS, s3FileExportEntity.tos3FileDetails(), TRANS_FETCH_EXCEL_FILE_FROM_S3, FALSE, headers)).switchIfEmpty(Mono.defer(() -> s3FileExportRepository.save(S3FileExportEntity.of(reportId, shortCode, reportName)).flatMap(s3FileExportEntity -> responseMapper.setApiResponse(ERR_SUCCESS, s3FileExportEntity.tos3FileDetails(), TRANS_FETCH_EXCEL_FILE_FROM_S3, FALSE, headers))));
    }

    public Mono<WsResponse> extractAndUpdateAllocations(Map<String, String> headers, InputStream inputStream, LocalDate startDate, LocalDate endDate, String productName, String allocationFor, Boolean isUpdate) {
        try {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            Iterator<Row> rowIterator = sheet.iterator();
            List<ExcelData> excelDataList = new ArrayList<>();

            return Flux.fromIterable(() -> rowIterator).skip(1) // Skip the header row
                    .flatMap(row -> {
                        try {
                            // Read values from the Excel row
                            String productCode = row.getCell(0).getStringCellValue();
                            String dealerCode = row.getCell(1).getStringCellValue();
                            int newAllocation = (int) row.getCell(2).getNumericCellValue();

                            if (productCode == null || productCode.isEmpty()) {
                                return Mono.error(new ExcelDataValidationException("Product code cannot be empty for row number " + row.getRowNum()));
                            }

                            if (dealerCode == null || dealerCode.isEmpty()) {
                                return Mono.error(new ExcelDataValidationException("Dealer code cannot be empty for row number " + row.getRowNum()));
                            }

                            if (newAllocation == 0) {
                                return Mono.error(new ExcelDataValidationException("Please specify valid allocation amount on row number " + row.getRowNum()));
                            }

                            // Fetch categories and check for product code existence
                            return productCategoryRepository.findByCategoryName(productName).collectList() // Collect all product categories for the product name
                                    .flatMap(productCategories -> {
                                        if (productCategories.isEmpty()) {
                                            return Mono.error(new ProductCodeNotFoundException("Product category " + productName + " not found."));
                                        }

                                        // Fetch products by category ID and check if the product code exists
                                        return Flux.fromIterable(productCategories).flatMap(productCategory -> productRepository.findByCategoryId(productCategory.getCategoryId())).map(Product::getProductCode).collectList().flatMap(productCodes -> {
                                            if (!productCodes.contains(productCode)) {
                                                return Mono.error(new ProductCodeNotFoundException("Product code " + productCode + " not found in product category " + productName));
                                            }

                                            // Create ExcelData object for valid entries
                                            ExcelData excelData = new ExcelData();
                                            excelData.setProductCode(productCode.trim());
                                            excelData.setDealerCode(dealerCode.trim());
                                            excelData.setAllocation(newAllocation);

                                            return Mono.just(excelData);
                                        });
                                    });
                        } catch (Exception ex) {
                            return Mono.error(ex);
                        }
                    }).collectList() // Collect all validated ExcelData into a List
                    .flatMap(validatedDataList -> {
                        excelDataList.addAll(validatedDataList); // Populate the shared list (optional)

                        // Continue processing the list and return a CustomResponse
                        return updateAllocation(headers, validatedDataList, startDate, endDate, productName, allocationFor, isUpdate);
                    }).onErrorResume(error -> customResponse(headers, error.getLocalizedMessage(), ERR_BAD_REQUEST, TRANS_ADD_CART));

        } catch (Exception ex) {
            return customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_ADD_CART);
        }

    }

    private Mono<WsResponse> updateAllocation(Map<String, String> headers,
                                              List<ExcelData> excelData,
                                              LocalDate startDate,
                                              LocalDate endDate,
                                              String productName,
                                              String allocationFor,
                                              boolean isUpdate) {

        return Flux.fromIterable(excelData)
                .flatMap(data -> updateAllocations(
                        headers,
                        data.getDealerCode(),
                        data.getProductCode(),
                        data.getAllocation(),
                        startDate,
                        endDate,
                        productName,
                        allocationFor,
                        isUpdate
                ))
                .then(Mono.defer(() ->
                        responseMapper.setApiResponse(ERR_SUCCESS, null, "", FALSE, headers)
                ));
    }

    public Mono<ProductAllocationEntity> updateAllocations(
            Map<String, String> headers,
            String dealerCode,
            String productCode,
            int newAllocation,
            LocalDate startDate,
            LocalDate endDate,
            String productName,
            String allocationFor,
            boolean isUpdate
    ) {
        return productAllocationRepository
                .findProductAllocationWithDates(dealerCode, productCode, startDate, endDate)
                .flatMap(entity -> {
                    if (!isUpdate) {
                        return logsAllocationRepository
                                .findByAllocationIdAndAllocationFor(entity.getId(), allocationFor)
                                .count()
                                .flatMap(count -> {
                                    if (count > 0) {
                                        return Mono.error(new AllocationConflictException(
                                                "There is an existing allocation for dealer code " + dealerCode +
                                                        " and product code " + productCode +
                                                        " for the specified dates. Start Date: " + startDate +
                                                        " End Date: " + endDate));
                                    }

                                    // proceed with saving if no conflict
                                    entity.setAllocation(entity.getAllocation() + newAllocation);
                                    entity.setUpdatedBy(headers.get(X_PARTNER_IDENTITY));
                                    entity.setUpdatedOn(localDateTime());

                                    return productAllocationRepository.save(entity)
                                            .flatMap(saved ->
                                                    logsAllocationRepository
                                                            .save(buildLog(
                                                                    saved.getId(),
                                                                    dealerCode, productCode, productName,
                                                                    newAllocation, false, startDate, endDate,
                                                                    allocationFor, headers.get(X_PARTNER_IDENTITY)))
                                                            .thenReturn(saved)
                                            );
                                });
                    }

                    entity.setAdditionalAllocation(entity.getAdditionalAllocation() + newAllocation);
                    entity.setAllocation(entity.getAllocation() + newAllocation);
                    entity.setUpdatedBy(headers.get(X_PARTNER_IDENTITY));
                    entity.setUpdatedOn(localDateTime());

                    return productAllocationRepository.save(entity)
                            .flatMap(saved ->
                                    logsAllocationRepository
                                            .save(buildLog(
                                                    saved.getId(),
                                                    dealerCode, productCode, productName,
                                                    newAllocation, true, startDate, endDate,
                                                    allocationFor, headers.get(X_PARTNER_IDENTITY)))
                                            .thenReturn(saved)
                            );
                })
                .switchIfEmpty(Mono.defer(() -> {
                    if (isUpdate) {
                        return Mono.error(new AllocationConflictException(
                                "No allocation was found for dealer " + dealerCode +
                                        " and product " + productCode +
                                        " in the given dates, so it cannot be supplementary."));
                    }
                    // Create new entity
                    ProductAllocationEntity entity = new ProductAllocationEntity();
                    entity.setDealerCode(dealerCode);
                    entity.setProductCode(productCode);
                    entity.setProductName(productName);
                    entity.setAllocation(newAllocation);
                    entity.setAdditionalAllocation(0);
                    entity.setStartDate(startDate);
                    entity.setEndDate(endDate);
                    entity.setCreated(localDateTime());
                    entity.setUpdatedOn(localDateTime());
                    entity.setCreatedBy(headers.get(X_PARTNER_IDENTITY));
                    entity.setUpdatedBy(headers.get(X_PARTNER_IDENTITY));
                    entity.setUpdatedStatus(true);
                    entity.setOrdered(0);

                    return productAllocationRepository.save(entity)
                            .flatMap(saved ->
                                    logsAllocationRepository
                                            .save(buildLog(
                                                    saved.getId(),
                                                    dealerCode, productCode, productName,
                                                    newAllocation, false, startDate, endDate,
                                                    allocationFor, headers.get(X_PARTNER_IDENTITY)))
                                            .thenReturn(saved)
                            );
                }))
                .onErrorResume(ex -> {
                    starterInfo(headers.get(X_CONVERSATION_ID), "update allocation", "", ex.getLocalizedMessage());

                    return Mono.error(ex);

                });
    }

    private LogsAllocationEntity buildLog(
            Long allocationId,
            String dealerCode,
            String productCode,
            String productName,
            int allocation,
            boolean isSupplementary,
            LocalDate specifiedStartDate,
            LocalDate specifiedEndDate,
            String allocationFor,
            String updatedBy
    ) {
        LogsAllocationEntity log = new LogsAllocationEntity();
        log.setAllocationId(allocationId);
        log.setDealerCode(dealerCode);
        log.setProductCode(productCode);
        log.setProductName(productName);
        log.setAllocation(allocation);
        log.setIsSupplementary(isSupplementary);
        log.setSpecifiedStartDate(specifiedStartDate);
        log.setSpecifiedEndDate(specifiedEndDate);
        log.setAllocationFor(allocationFor);
        log.setUpdatedBy(updatedBy);
        log.setUpdatedOn(LocalDateTime.now());
        return log;
    }

    public Mono<SavedOrderResponse> houseKeepingForEachSavedOrderItem(SavedOrderEntity savedOrder) {
        return savedOrderItemRepository.findByOrderIdentifierId(savedOrder.getId()).map(savedOrderItem -> savedOrderItem.toSavedOrderItemResponse(savedOrderItem)).collectList().flatMap(savedOrderItemResponses -> Mono.just(savedOrder.toSavedOrderResponse(savedOrderItemResponses)));
    }

    public Mono<ItemCount> getOrderQuantityCount(Map<String, String> headers, String dealerCode, String from, String to, String productCode) {
        return client.getOrderQuantityCount(headers.get(X_CONVERSATION_ID)).get().uri(String.format("?dealerCode=%s&from=%s&to=%s&productCode=%s", dealerCode, from, to, productCode)).header(X_MSISDN, MSISDN).retrieve().bodyToMono(DxlItemCountResponse.class).flatMap(response -> {
            if (response == null) return Mono.empty();
            if (response.getBody() == null) return Mono.empty();
            return Mono.just(response.getBody());
        });
    }

    public Mono<WsResponse> executeWithInputAndSessionValidation(Mono<ErrorMapping> requestValidation,
                                                                 Function<ErrorMapping, Mono<TokenErrorMapping>> ssoValidation,
                                                                 Function<PartnerInfo, Mono<WsResponse>> businessLogic,
                                                                 ValidationContext context
    ) {
        return requestValidation
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() != null) {
                        context.partnerInfo().setReportName("Validation failed");
                        this.streamToKafka(context.headers(), context.partnerInfo(), ERR_BAD_REQUEST, null);
                        return this.customResponse(context.headers(), context.partnerInfo(), errorMapping.getErrorMessage(), ERR_VALIDATION, context.params());
                    }
                    return ssoValidation.apply(errorMapping)
                            .flatMap(tokenErrorMapping -> {
                                if (tokenErrorMapping.getErrorMessage() != null) {
                                    context.partnerInfo().setReportName("SSO Token validation failed");
                                    this.streamToKafka(context.headers(), context.partnerInfo(), ERR_BAD_REQUEST, null);
                                    return this.customResponse(context.headers(), context.partnerInfo(), tokenErrorMapping.getErrorMessage(), ERR_VALIDATION, context.params());
                                }
                                context.partnerInfo().setRole(tokenErrorMapping.getRole());
                                context.partnerInfo().setEmail(tokenErrorMapping.getEmail());
                                context.partnerInfo().setPhone(tokenErrorMapping.getPhone());
                                return businessLogic.apply(context.partnerInfo());
                            });
                })
                .doOnError(err -> starterError(context.headers().get(X_CONVERSATION_ID), context.process(), "", err.getMessage()))
                .onErrorResume(throwable -> this.customResponse(context.headers(), context.partnerInfo(), REQUEST_FAILED, context.code(), context.params()));
    }
}
