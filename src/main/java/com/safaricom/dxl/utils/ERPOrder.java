package com.safaricom.dxl.utils;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.safaricom.dxl.data.model.OrderRequest;
import com.safaricom.dxl.data.xml.CreateOrderResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.StringReader;
import java.util.List;
import java.util.Map;

import static com.safaricom.dxl.utils.Utilities.getXmlMapper;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;

@Service
@AllArgsConstructor
public class ERPOrder {

    public Mono<CreateOrderResponse.Response> create(Map<String, String> headers, String dealerCode, List<OrderRequest> orderRequests, MyWebClient client, String requestId, String systemRef) {
        String body = createOrderXml(dealerCode, orderRequests, requestId, systemRef);
        return client.createOrder().post()
                .contentType(MediaType.TEXT_XML)
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .flatMap(response -> {
                    XmlMapper xmlMapper = getXmlMapper();
                    //Create Response object (POJO)
                    try {
                        CreateOrderResponse xmlResponse = xmlMapper.readValue(new StringReader(response), CreateOrderResponse.class);
                        return Mono.just(xmlResponse.getBodyMainResponse().getResponse());
                    } catch (Exception e) {
                        starterError(headers.get(X_CONVERSATION_ID), "createOrder error", "", e.getMessage());
                        return Mono.empty();
                    }
                });
    }

    public String createOrderXml(String dealerCode, List<OrderRequest> orderRequests, String requestId, String systemRef) {
        String ordersXml = """
                <ns2:Line>
                          <ns2:ItemCode>%s</ns2:ItemCode>
                          <ns2:Quantity>%s</ns2:Quantity>
                          <ns2:Warehouse>%s</ns2:Warehouse>
                          <ns2:ShipTo>%s</ns2:ShipTo>
                          <ns2:FOC>N</ns2:FOC>
                </ns2:Line>
                """;

        StringBuilder sb = new StringBuilder();
        int counter = 0;
        for (OrderRequest orderRequest : orderRequests){
            if(counter > 0) sb.append("\n");
            sb.append(ordersXml.formatted(orderRequest.getProductCode(), orderRequest.getQuantity(), orderRequest.getMiniStoreId(), orderRequest.getShipToId()));
            counter++;
        }

        return """
                <SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://xmlns.telco.com/EAI/Common/Header" xmlns:ns2="http://xmlns.telco.com/ServiceCatalog/Business/CreateDealerSalesOrders_v1">
                                     <SOAP-ENV:Body>
                                       <ns1:Header>
                                         <ns1:RequestId>%s</ns1:RequestId>
                                         <ns1:SourceSystem>%s</ns1:SourceSystem>
                                         <ns1:Service>CreateDealerSalesOrders</ns1:Service>
                                         <ns1:ReplyExpected>Now</ns1:ReplyExpected>
                                         <ns1:QoS>R</ns1:QoS>
                                         <ns1:Version>1</ns1:Version>
                                         <ns1:Credentials>
                                           <ns1:ApplicationId>EAI</ns1:ApplicationId>
                                           <ns1:User>safaricom</ns1:User>
                                         </ns1:Credentials>
                                       </ns1:Header>
                                       <ns2:Request>
                                         <ns2:CustomerNumber>%s</ns2:CustomerNumber>
                                         <ns2:OrderTypeId>1002</ns2:OrderTypeId>
                                         <ns2:LineRecordset>
                                             %s
                                         </ns2:LineRecordset>
                                         <ns2:PODoc></ns2:PODoc>
                                         <ns2:POReference>1</ns2:POReference>
                                       </ns2:Request>
                                     </SOAP-ENV:Body>
                                   </SOAP-ENV:Envelope>
                """.formatted(requestId, systemRef, dealerCode, sb);
    }
}
