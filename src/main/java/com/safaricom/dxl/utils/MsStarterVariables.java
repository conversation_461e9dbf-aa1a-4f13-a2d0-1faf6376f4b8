package com.safaricom.dxl.utils;

public class MsStarterVariables {
    MsStarterVariables() {
    }

    public static final String ERR_SUCCESS = "200";
    public static final String ERR_BAD_REQUEST = "400";
    public static final String ERR_NOT_FOUND = "404";
    public static final String ERR_VALIDATION = "465";
    public static final String ERR_CUSTOM_VALIDATION = "466";
    public static final String SERVICE_NAME = "ms-partner-ordering";
    public static final String TRANS_ADD_CART = "ADD,CART,POSTGRESDB";
    public static final String TRANS_FETCH_CART = "FETCH,CART,POSTGRESDB";
    public static final String TRANS_ADD_SAVED_ORDER = "ADD,SAVED_ORDER,POSTGRESDB";
    public static final String TRANS_FETCH_ORDER = "FETCH,ORDER,POSTGRESDB";
    public static final String TRANS_FETCH_SAVED_ORDER = "FETCH,SAVED_ORDER,POSTGRESDB";
    public static final String TRANS_DELETE_CART = "DELETE,CART,POSTGRESDB";
    public static final String TRANS_DELETE_SAVED_ORDER = "DELETE,SAVED_ORDER,POSTGRESDB";
    public static final String TRANS_ADD_ORDER = "ADD,ORDER,POSTGRESDB/ERP";
    public static final String TRANS_SEND_SMS = "API,SMS_TX,DXL_API";
    public static final String TRANS_FETCH_PRODUCT_ALLOCATION_DETAILS = "FETCH,ALLOCATION,POSTGRES_DB";
    public static final String USERNAME_VALIDATION_REGEX = "^[\\w\\s]{1,20}$";
    public static final String TRANS_ADD_NOMINEE = "ADD,NOMINEE,POSTGRESDB";
    public static final String TRANS_FETCH_NOMINEE = "FETCH,NOMINEE,POSTGRESDB";
    public static final String TRANS_RETIRE_NOMINEE = "RETIRE,NOMINEE,POSTGRESDB";
    public static final String TRANS_FETCH_MINISTORES = "FETCH,MINISTORES,POSTGRESDB";
    public static final String TRANS_FETCH_EXCEL_FILE_FROM_S3 = "FETCH,EXCEL,S3";
    public static final String TRANS_EXCEL_CREATION = "CREATE,EXCEL,S3";
    public static final String XLSX_SUFFIX = "xlsx";
    public static final String FILENAME_FORMAT = "%s_%s.%s";
    public static final String FILE_PATH_NAME_FORMAT = "%s/%s";
    public static final String SHEET_NAME_FORMAT = "%s %s_%s";
    public static final String X_PARTNER_IDENTITY = "x-partner-identity";
    public static final String DEALER_CODE_REGEX = "^[A-Z0-9_-]+$";
    public static final String INTEGER_REGEX = "^\\d+$";
    public static final String DOUBLE_REGEX = "^[+-]?(\\d+(\\.\\d{1,2})?|(\\.\\d{1,2}))$";
    public static final String UUID_REGEX = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$";
    public static final String TRANS_STRING_DATE_REGEX = "^((1[6-9]|[2-9]\\d)\\d{2})-(((0[13578]|1[02])-31)|((0[1,3-9]|1[0-2])-(29|30)))$|^((((1[6-9]|[2-9]\\d)?(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00)))-02-29)$|^((1[6-9]|[2-9]\\d)\\d{2})-((0[1-9])|(1[0-2]))-(0[1-9]|1\\d|2[0-8])$";
    public static final String PRODUCT_CODE_REGEX = "^([A-Z]\\d{7}|[A-Z]{3}\\d{5})$";
    public static final String MSISDN = "0722000000";
}
