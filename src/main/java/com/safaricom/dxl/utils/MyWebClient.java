package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.MsDIProperties;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.util.Map;

import static com.safaricom.dxl.utils.Utilities.getRefId;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;
import static org.springframework.http.HttpHeaders.ACCEPT;
import static org.springframework.http.HttpHeaders.ACCEPT_ENCODING;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@Service
@AllArgsConstructor
public class MyWebClient {
    private MsDIProperties msDIProperties;
    private MsConfigProperties properties;

    public HttpClient httpClient() {
        return HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 60_000)
                .doOnConnected(c -> c
                        .addHandlerLast(new ReadTimeoutHandler(60))
                        .addHandlerLast(new WriteTimeoutHandler(60)));
    }

    public WebClient sendDxlSms(Map<String, String> headers) {
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient()))
                .defaultHeader(HEADER_CONTENT_TYPE, CONTENT_TYPE)
                .defaultHeader(ACCEPT_ENCODING, APPLICATION_JSON_VALUE)
                .defaultHeader(ACCEPT, APPLICATION_JSON_VALUE)
                .defaultHeader(X_CONVERSATION_ID, headers.get(X_CONVERSATION_ID))
                .defaultHeader(X_MESSAGE_ID, headers.get(X_MESSAGE_ID))
                .defaultHeader("x-api-key", properties.getDxlApiKey())
                .defaultHeader(X_SOURCE_SYSTEM, headers.get(X_SOURCE_SYSTEM))
                .defaultHeader(X_APP, headers.get(X_APP))
                .defaultHeader(X_VERSION, "1.0.0")
                .defaultHeader(X_DEVICE_TOKEN, headers.get(X_CONVERSATION_ID))
                .defaultHeader(X_MSISDN, headers.get(X_MSISDN))
                .defaultHeader(HEADER_AUTHORIZATION, properties.getDxlAuth())
                .baseUrl(properties.getDxlEndpoint())
                .build();
    }

    public WebClient validateSsoTokenSts() {
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient()))
                .defaultHeaders(httpHeaders -> httpHeaders.addAll(stsHeaders(null)))
                .baseUrl(msDIProperties.getValidateTokenStsUrl())
                .build();
    }

    public HttpHeaders stsHeaders(String convoId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(X_CONVERSATION_ID, convoId == null ? getRefId() : convoId);
        headers.set(X_SOURCE_SYSTEM, msDIProperties.getPartnerSourceSystem());
        headers.set(X_APP, msDIProperties.getSourceSystem());
        headers.set(HEADER_CONTENT_TYPE, CONTENT_TYPE);
        headers.set(X_MESSAGE_ID, msDIProperties.getXMessageId());
        headers.set(HEADER_AUTHORIZATION, msDIProperties.getBasicAuth());
        return headers;
    }

    public WebClient createOrder() {
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient()))
                .defaultHeaders(httpHeaders -> httpHeaders.addAll(stsHeaders(null)))
                .baseUrl(properties.getCreateOrderUrl())
                .build();
    }

    public WebClient getOrderQuantityCount(String convoId) {
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient()))
                .defaultHeaders(httpHeaders -> httpHeaders.addAll(stsHeaders(convoId)))
                .baseUrl(properties.getOrderQuantityCountUrl())
                .build();
    }
}
