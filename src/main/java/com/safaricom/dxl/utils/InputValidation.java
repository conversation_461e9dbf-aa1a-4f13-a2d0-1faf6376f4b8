package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.data.dto.CartDto;
import com.safaricom.dxl.data.dto.CartItemsDto;
import com.safaricom.dxl.data.dto.SaveOrderItemsDto;
import com.safaricom.dxl.data.model.ErrorMapping;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Utilities.regexValidation;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;

@Service
@AllArgsConstructor
public class InputValidation {

    private final MsConfigProperties properties;

    public ErrorMapping dealerCode(String dealerCode) {
        if (dealerCode != null && dealerCode.length() >= 6 && dealerCode.length() <= 10 && regexValidation(dealerCode, DEALER_CODE_REGEX))
            return new ErrorMapping();
        return new ErrorMapping("Invalid dealer code.");
    }

    public ErrorMapping username(Map<String, String> headers, ErrorMapping errorMapping) {
        if (regexValidation(headers.get(X_IDENTITY), properties.getIdentityValidation()))
            return errorMapping;
        errorMapping.setErrorMessage("Invalid username.");
        return errorMapping;
    }

    public ErrorMapping name(String string, String msg) {
        if (regexValidation(string, USERNAME_VALIDATION_REGEX))
            return new ErrorMapping();
        return new ErrorMapping(msg);
    }

    public Mono<ErrorMapping> codeAndProductCode(Map<String, String> headers, String dealerCode, String productCode) {
        return codeAndUserName(headers, dealerCode)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null)
                        return Mono.just(productCode(errorMapping, productCode));
                    return Mono.just(errorMapping);
                });
    }

    public ErrorMapping productCode(ErrorMapping errorMapping, String productCode) {
        if (regexValidation(productCode, PRODUCT_CODE_REGEX))
            return errorMapping;
        errorMapping.setErrorMessage("Invalid product code");
        return errorMapping;
    }

    public ErrorMapping number(String string) {
        if (regexValidation(string, "^\\d+$"))
            return new ErrorMapping();
        return new ErrorMapping("Invalid id");
    }

    public Mono<ErrorMapping> codeAndUserName(Map<String, String> headers, String dealerCode){
        ErrorMapping shortCodeError = dealerCode(dealerCode);
        if (shortCodeError.getErrorMessage() == null)
            return Mono.just(username(headers, shortCodeError));
        return Mono.just(shortCodeError);
    }

    public Mono<ErrorMapping> cartPayload(Map<String, String> headers, CartDto dto) {
        return codeAndUserName(headers, dto.getDealerCode())
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null)
                        return Mono.just(name(dto.getProductCode(), "Invalid Product Code."));
                    return Mono.just(errorMapping);
                });
    }

    public Mono<ErrorMapping> codeAndUserNameAndOrderNumber(Map<String, String> headers, String dealerCode, String orderNumber){
        return codeAndUserName(headers, dealerCode)
                .flatMap(codeAndUserNameError -> {
                    if (codeAndUserNameError.getErrorMessage() == null)
                        return Mono.just(name(orderNumber, "Invalid Order Number."));
                    return Mono.just(codeAndUserNameError);
                });
    }

    public Mono<ErrorMapping> createOrderDto(Map<String, String> headers, String dealerCode, CartItemsDto dto) {
        return codeAndUserName(headers, dealerCode)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        if (!dto.getCartIds().isEmpty())
                            return Mono.just(errorMapping);
                        return Mono.just(new ErrorMapping("Cart ids cannot be empty"));
                    }
                    return Mono.just(errorMapping);
                });
    }

    public Mono<ErrorMapping> fetchCartItemsRequest(Map<String, String> headers, String dealerCode, String userId) {
        if (userId != null && !userId.isEmpty() && regexValidation(userId, INTEGER_REGEX)){
            return codeAndUserName(headers, dealerCode);
        }
        return Mono.just(new ErrorMapping("Invalid userId"));
    }

    public Mono<ErrorMapping> fetchSavedOrderRequest(Map<String, String> headers, String dealerCode, String orderIdentifier) {
        if (orderIdentifier != null && !orderIdentifier.isEmpty() && regexValidation(orderIdentifier, UUID_REGEX)) {
            return codeAndUserName(headers, dealerCode);
        }
        return Mono.just(new ErrorMapping("Invalid userId"));
    }

    public Mono<ErrorMapping> fetchSavedCartItemsRequest(Map<String, String> headers, String dealerCode, String userId) {
        return fetchCartItemsRequest(headers, dealerCode, userId);
    }

    public Mono<ErrorMapping> deleteSavedCartItemsRequest(Map<String, String> headers, String dealerCode, String savedCartId) {
        return fetchCartItemsRequest(headers, dealerCode, savedCartId);
    }

    public Mono<ErrorMapping> fetchOrderByDealerCodeRequest(Map<String, String> headers, String dealerCode, int pageSize, String from, String to) {
        return codeAndUserName(headers, dealerCode)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return pagingValidation(errorMapping, pageSize)
                                .flatMap(pageError -> {
                                    if (pageError.getErrorMessage() == null){
                                        ErrorMapping fromError = startDate(pageError, from);
                                        if (fromError.getErrorMessage() == null)
                                            return Mono.just(endDate(fromError, to));
                                        return Mono.just(fromError);
                                    }
                                    return Mono.just(pageError);
                                });
                    }
                    return Mono.just(errorMapping);
                });
    }

    public Mono<ErrorMapping> pagingValidation(ErrorMapping errorMapping, int pageSize){
        if (pageSize > 200){
            errorMapping.setErrorMessage("Page size exceeded");
            return Mono.just(errorMapping);
        }
        return Mono.just(errorMapping);
    }

    public ErrorMapping startDate(ErrorMapping errorMapping, String date) {
        if (date != null && date.matches(TRANS_STRING_DATE_REGEX)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate dob = LocalDate.parse(date, formatter);
            if (Period.between(dob, LocalDate.now()).getMonths() <= 6)
                return errorMapping;
            errorMapping.setErrorMessage("No records for more than six months.");
            return errorMapping;
        }
        errorMapping.setErrorMessage("Please enter a valid start date. Sample format: 2024-05-01");
        return errorMapping;
    }

    public ErrorMapping endDate(ErrorMapping errorMapping, String date) {
        if (!regexValidation(date,  TRANS_STRING_DATE_REGEX))
            errorMapping.setErrorMessage("Please enter a valid end date. Sample format: 2024-06-14");
        return errorMapping;
    }
    public Mono<ErrorMapping> codeAndDate(Map<String, String> headers, String shortCode, String startDate, String endDate){
        ErrorMapping errorMapping = dealerCode(shortCode);
        if (errorMapping.getErrorMessage() == null){
            ErrorMapping startDateError = startDate(errorMapping, startDate);
            if (startDateError.getErrorMessage() == null) {
                ErrorMapping endDateError = endDate(startDateError, endDate);
                if (endDateError.getErrorMessage() == null)
                    return Mono.just(username(headers, endDateError));
                return Mono.just(endDateError);
            }
            return Mono.just(startDateError);
        }
        return Mono.just(errorMapping);
    }

    public Mono<ErrorMapping> savedOrderPayload(Map<String, String> headers, String dealerCode, SaveOrderItemsDto dto) {
        return codeAndUserName(headers, dealerCode)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        if (dto.getOrderItems().size() <= properties.getAllowedOrderItems()) {
                            return Mono.just(errorMapping);
                        }
                        errorMapping.setErrorMessage(String.format("Maximum items in a saved order is %s.", properties.getAllowedOrderItems()));
                        return Mono.just(errorMapping);
                    }
                    return Mono.just(errorMapping);
                });
    }
}
