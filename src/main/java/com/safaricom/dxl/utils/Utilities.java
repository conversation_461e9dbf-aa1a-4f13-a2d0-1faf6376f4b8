package com.safaricom.dxl.utils;

import com.fasterxml.jackson.dataformat.xml.JacksonXmlModule;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.data.model.DxlSmsPayload;
import com.safaricom.dxl.data.model.SenderReceiverDetails;
import lombok.RequiredArgsConstructor;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeoutException;
import java.util.regex.Pattern;

@Component
@RequiredArgsConstructor
public class Utilities {
    private final MsConfigProperties properties;

    public static boolean regexValidation(String text, String pattern) {
        String regex = pattern == null ? "^(254|0)?[71]\\d{8}$" : pattern;
        if (text == null || text.isEmpty()) return false;
        Pattern p = Pattern.compile(regex);
        return p.matcher(text.trim()).matches();
    }

    public static String stripString(String x){
        return x == null ? "" : x.replace("\u0000", "");
    }




    public static final String X_TOKEN = "x-token";
    public static String getRefId(){
        return UUID.randomUUID().toString();
    }
    public static String generateId(String value) {
        return value != null ? String.valueOf(UUID.nameUUIDFromBytes(value.getBytes())) : null;
    }
    /**
     * Remove leading 0 or 254 from msisdn
     */
    public static String formatMsisdn(String msisdn) {
        return msisdn.length() > 9 ? msisdn.substring(msisdn.length() - 9) : msisdn;
    }

    static XmlMapper getXmlMapper() {
        JacksonXmlModule module = new JacksonXmlModule();
        module.setDefaultUseWrapper(false);
        return new XmlMapper(module);
    }

    public static final String AFRICA_NAIROBI_ZONE = "Africa/Nairobi";

    public static Pageable pagination(int pageNo, int pageSize, Sort sort) {
        if (pageSize < 1) pageSize = 1;
        pageNo = pageNo <= 0 ? 0 : pageNo - 1;
        return PageRequest.of(pageNo, pageSize, sort);
    }

    public static String getSmsBody(String msisdn, String message, String orderNumber) {
        return new JSONObject()
                .put("roles", new JSONObject()
                        .put("receiver", new JSONObject()
                                .put("id", new JSONArray()
                                        .put(new JSONObject()
                                                .put("value", msisdn)
                                        )
                                )
                        )
                )
                .put("parts", new JSONObject()
                        .put("body", new JSONObject()
                                .put("text", String.format(message, orderNumber))
                        )
                        .put("trailer", new JSONObject()
                                .put("text", "Safaricom")
                        )
                ).toString();
    }

    public static String getNomineeSmsBody(String msisdn, String message) {
        return new JSONObject()
                .put("roles", new JSONObject()
                        .put("receiver", new JSONObject()
                                .put("id", new JSONArray()
                                        .put(new JSONObject()
                                                .put("value", msisdn)
                                        )
                                )
                        )
                )
                .put("parts", new JSONObject()
                        .put("body", new JSONObject()
                                .put("text", message)
                        )
                        .put("trailer", new JSONObject()
                                .put("text", "Safaricom")
                        )
                ).toString();
    }

    public static DxlSmsPayload getDxlSmsBody(String msisdn, String message, String orderNumber, MsConfigProperties properties) {
        List<SenderReceiverDetails> receiver = new ArrayList<>();
        SenderReceiverDetails senderReceiverDetails = SenderReceiverDetails.builder()
                .name("")
                .phoneNumber(msisdn)
                .build();
        receiver.add(senderReceiverDetails);

        SenderReceiverDetails sender = SenderReceiverDetails.builder()
                .name(properties.getSmsSenderId())
                .phoneNumber(properties.getSmsSenderId())
                .build();

        return DxlSmsPayload.builder()
                .sender(sender)
                .receiver(receiver)
                .content(String.format(message, orderNumber))
                .messageType("sms")
                .type("sms")
                .priority("1")
                .interactive("false")
                .callBackUrl("")
                .build();
    }

    /**
     * Determines whether an exception should trigger a retry.
     * Only retries for network-related issues and 5xx server errors.
     *
     * @param throwable The exception that occurred
     * @return true if the request should be retried, false otherwise
     */
    public static boolean shouldRetry(Throwable throwable) {
        // Retry for network connectivity issues
        if (throwable instanceof TimeoutException ||
                throwable instanceof IOException) {
            return true;
        }

        // Retry for WebClient response exceptions with 5xx status codes
        if (throwable instanceof WebClientResponseException webClientResponseException) {
            HttpStatus status = HttpStatus.resolve(webClientResponseException.getStatusCode().value());

            // Only retry for 5xx server errors (500, 502, 503, 504, etc.)
            return status != null && status.is5xxServerError();
        }

        // Don't retry for other exceptions (4xx client errors, business logic errors, etc.)
        return false;
    }

    public static String s3FileId(String shortCode, String start, String end, String reportName){
        return generateId(shortCode + start + end + reportName);
    }

    public static LocalDate localDate(){
        return new Date().toInstant().atZone(ZoneId.of(AFRICA_NAIROBI_ZONE)).toLocalDate();
    }

    public static LocalDate localDatePlus(int days){
        return new Date().toInstant().atZone(ZoneId.of(AFRICA_NAIROBI_ZONE)).plusDays(days).toLocalDate();
    }

    public static LocalDate localDateMinus(int days){
        return new Date().toInstant().atZone(ZoneId.of(AFRICA_NAIROBI_ZONE)).minusDays(days).toLocalDate();
    }

    public static LocalDateTime localDateTime(){
        return new Date().toInstant().atZone(ZoneId.of(AFRICA_NAIROBI_ZONE)).toLocalDateTime();
    }

    public static long milliSeconds(){
        return localDateTime().atZone(ZoneId.of(AFRICA_NAIROBI_ZONE)).toInstant().toEpochMilli();
    }

    public static LocalDateTime localDateTimeMinus(int days){
        return new Date().toInstant().atZone(ZoneId.of(AFRICA_NAIROBI_ZONE)).minusDays(days).toLocalDateTime();
    }

    public static LocalDateTime fromFormattedDate(String date){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(date, formatter).atStartOfDay();
    }

    public static LocalDateTime toFormattedDate(String date){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(date, formatter).plusDays(1).atStartOfDay();
    }

    public static String getDayOfCurrentMonthString(boolean firstDay) {
        String format = "dd-MMM-yyyy";
        if (firstDay) {
            // Get the year and month from the current date
            YearMonth yearMonth = YearMonth.from(localDateTime());
            // Get the first day of the month
            return yearMonth.atDay(1).format(formatter(format));
        } else {
            // Get the current day of the month
            return localDateTime().format(formatter(format));
        }
    }

    public static LocalDateTime getDayOfCurrentMonth(boolean firstDay) {
        // Get the year and month from the current date
        YearMonth yearMonth = YearMonth.from(localDateTime());
        if (firstDay) {
            // Get the first day of the month
            return yearMonth.atDay(1).atStartOfDay();
        } else {
            // Get the current day of the month
            return yearMonth.atEndOfMonth().atTime(23, 59, 59, 999_999_999);
        }
    }

    public static String stringLocalDateTime(LocalDate time, boolean firstDay) {
        String format = "yyyy-MM-dd HH:mm:ss";
        if (firstDay)
            return time.atStartOfDay().format(formatter(format));
        return time.atTime(23, 59, 59, 999_999_999).format(formatter(format));
    }

    public static DateTimeFormatter formatter(String format){
        return DateTimeFormatter.ofPattern(format);
    }
}
