package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.MsDIProperties;
import com.safaricom.dxl.data.model.DxlApiResponse;
import com.safaricom.dxl.data.model.ErrorMapping;
import com.safaricom.dxl.data.model.TokenErrorMapping;
import com.safaricom.dxl.data.postgres.repositories.UsersRepository;
import com.safaricom.dxl.data.redis.CacheRepository;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import static com.safaricom.dxl.utils.Utilities.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_CONVERSATION_ID;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_MSISDN;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class SSOToken {
    private final MsDIProperties properties;
    private final MsConfigProperties msConfigProperties;
    private final CacheRepository cacheRepository;
    private final MyWebClient client;
    private final UsersRepository usersRepository;
    private final WsStarterService wsStarterService;

    public Mono<TokenErrorMapping> validateSessionAndCode(Map<String, String> headers, String dealerCode){
        return validation(headers)
                .flatMap(tokenErrorMapping -> {
                    if (tokenErrorMapping.getErrorMessage() == null) {
                        String keyForMatchingCode = generateId("validateSessionAndCode".concat(headers.get(X_TOKEN).trim()));
                        return cacheRepository.getCachedData(keyForMatchingCode)
                                .flatMap(role -> {
                                    tokenErrorMapping.setRole(role.trim());
                                    return Mono.just(tokenErrorMapping);
                                })
                                .switchIfEmpty(Mono.defer(() -> {
                                    if (tokenErrorMapping.getEmail() == null || tokenErrorMapping.getEmail().isEmpty()) {
                                        tokenErrorMapping.setErrorMessage("No Email found.");
                                        return Mono.just(tokenErrorMapping);
                                    }
                                    return validateUserCode(headers, dealerCode, tokenErrorMapping, keyForMatchingCode);
                                }));
                    } else {
                        return Mono.just(tokenErrorMapping);
                    }
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "validateSessionAndCode error", "", err.getMessage()));
    }

    private Mono<TokenErrorMapping> validateUserCode(Map<String, String> headers, String shortCode, TokenErrorMapping errorMapping, String keyForMatchingCode) {
        String formattedEmail = errorMapping.getEmail().trim().replace("\"", "");
        return usersRepository.findByEmail(formattedEmail)
                .flatMap(usersEntity -> {
                    List<String> roles = List.of(msConfigProperties.getRoleNotAllowedToQueryOtherDealers().split(","));
                    errorMapping.setRole(usersEntity.getRole());
                    //If it is a Director or Administrator.
                    if (roles.contains(usersEntity.getRole())) {
                        //If shortcode matches
                        if (!usersEntity.getCodes().isEmpty() && usersEntity.getCodes().contains(shortCode.toUpperCase())) {
                            return cacheRepository.cacheData(keyForMatchingCode, usersEntity.getRole(), Duration.ofMinutes(properties.getTtl()))
                                    .thenReturn(errorMapping);
                        }
                        //if shortcode do not match
                        errorMapping.setErrorMessage("User not allowed to check stats for other dealers");
                        return Mono.just(errorMapping);
                    } else {
                        //Other roles allowed to access stats for all dealers.
                        return cacheRepository.cacheData(keyForMatchingCode, usersEntity.getRole(), Duration.ofMinutes(properties.getTtl()))
                                .thenReturn(errorMapping);
                    }
                })
                .switchIfEmpty(Mono.defer(() -> Mono.just(new TokenErrorMapping("Your session has expired. Kindly Logout and login again afresh."))))
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "validateUserCode error", "", err.getMessage()));
    }

    public Mono<TokenErrorMapping> validation(Map<String, String> headers){
        if (properties.isSsoEnabled()){
            return validateForToken(headers)
                    .subscribeOn(Schedulers.boundedElastic())
                    .flatMap(errorMapping -> {
                        if (errorMapping.getErrorMessage() == null){
                            String uuidOfXToken = generateId(headers.get(X_TOKEN).trim());
                            return cacheRepository.getCachedData(uuidOfXToken)
                                    .flatMap(email -> cacheRepository.getCachedData("phone" + uuidOfXToken)
                                            .flatMap(phone -> Mono.just(new TokenErrorMapping(null, email, null, phone)))
                                            .switchIfEmpty(Mono.defer(() -> tokenValidationStsLogic(headers, uuidOfXToken))))
                                    .switchIfEmpty(Mono.defer(() -> tokenValidationStsLogic(headers, uuidOfXToken)));
                        } else {
                            return Mono.just(new TokenErrorMapping(errorMapping.getErrorMessage()));
                        }
                    });
        }
        return Mono.just(new TokenErrorMapping());
    }

    public Mono<ErrorMapping> validateForToken(Map<String, String> headers) {
        if (headers.get(X_TOKEN) == null || headers.get(X_TOKEN).isEmpty())
            return Mono.just(new ErrorMapping("SSO Token is missing."));
        else {
            return Mono.just(new ErrorMapping());
        }
    }

    private Mono<TokenErrorMapping> tokenValidationStsLogic(Map<String, String> headers, String uuidOfXToken) {
        AtomicReference<String> email = new AtomicReference<>("");
        AtomicReference<String> phone = new AtomicReference<>("");
        return client.validateSsoTokenSts().get()
                .header(X_MSISDN, formatMsisdn(headers.get(X_MSISDN)))
                .header(X_TOKEN, headers.get(X_TOKEN))
                .retrieve()
                .bodyToMono(DxlApiResponse.class)
                .flatMap(dxlApiResponse -> {
                    if (dxlApiResponse.getHeader().getResponseCode() == 200) {
                        JSONObject o = new JSONObject(wsStarterService.serializeToJson(dxlApiResponse.getBody()));
                        email.set(o.getString("emailAddress"));
                        phone.set((String) o.optJSONArray("msisdns").get(0));
                        if (email.get() != null || !email.get().isEmpty()) {
                            return cacheRepository.cacheData(uuidOfXToken, email.get().trim(), Duration.ofMinutes(properties.getTtl()))
                                    .then(cacheRepository.cacheData("phone" + uuidOfXToken, phone.get(), Duration.ofMinutes(properties.getTtl())))
                                    .thenReturn(new TokenErrorMapping(null, email.get(), null, phone.get()));
                        }
                        return Mono.just(new TokenErrorMapping(null, email.get(), null, phone.get()));
                    } else {
                        return Mono.just(new TokenErrorMapping("Session timed out. Please reload the page and login again"));
                    }
                })
                .doOnError(throwable -> starterError(headers.get(X_CONVERSATION_ID),"validateSsoTokenSts error","", throwable.getMessage()));
    }
}