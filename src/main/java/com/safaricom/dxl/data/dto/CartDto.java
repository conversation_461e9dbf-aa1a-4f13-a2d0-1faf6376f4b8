package com.safaricom.dxl.data.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import static com.safaricom.dxl.utils.MsStarterVariables.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class CartDto {
    @Valid
    @Pattern(regexp = DEALER_CODE_REGEX, message = "Please provide a valid dealer code")
    private String dealerCode;
    @Valid
    @Pattern(regexp = USERNAME_VALIDATION_REGEX, message = "Please provide a valid User Id")
    private String userId;
    @Valid
    @Pattern(regexp = USERNAME_VALIDATION_REGEX, message = "Please provide a valid product code")
    private String productCode;
    @Valid
    @Pattern(regexp = DOUBLE_REGEX, message = "Please provide a valid price")
    private String price;
    @Valid
    @Pattern(regexp = DOUBLE_REGEX, message = "Please provide a valid tax")
    private String tax;
    @Valid
    @Pattern(regexp = DOUBLE_REGEX, message = "Please provide a valid total")
    private String total;
    @Valid
    @Pattern(regexp = INTEGER_REGEX, message = "Please provide a valid quantity")
    private String quantity;
    @Valid
    @Pattern(regexp = INTEGER_REGEX, message = "Please provide a valid miniStoreId")
    private String miniStoreId;
    @Valid
    @Pattern(regexp = INTEGER_REGEX, message = "Please provide a valid shipToId")
    private String shipToId;
    private boolean stockAvailable;
}
