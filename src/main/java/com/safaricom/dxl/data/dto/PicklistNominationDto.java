package com.safaricom.dxl.data.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.safaricom.dxl.utils.MsStarterVariables.DEALER_CODE_REGEX;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PicklistNominationDto {

        @NotNull(message = "Document number cannot be null")
        @Pattern(regexp = "^[a-zA-Z0-9:?&=._/-]+$", message = "present a valid document number")
        private String documentNumber;

        @NotBlank(message = "First name cannot be null or empty")
        @Pattern(regexp = "^[\\w\\s\\']{1,20}$", message = "present a valid first name")
        @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
        private String firstName;


        private String middleName;

    @Pattern(regexp = "^[\\w\\s\\']{1,20}$", message = "present a valid last name")
        @NotBlank(message = "Last name cannot be null or empty")
        private String lastName;

        @NotBlank(message = "MiniStore name cannot be null or empty")
        @Size(max = 100, message = "MiniStore name must be up to 100 characters")
        private String miniStoreName;

        @NotBlank(message = "Document type cannot be null or empty")
        @Size(max = 20, message = "Document type must be up to 20 characters")
        private String documentType;

        @NotBlank(message = "Phone number cannot be null or empty")
        @Pattern(regexp = "^(254|0)?[71]\\d{8}$", message = "Please present a valid phone number")
        private String phoneNumber;

        @NotBlank(message = "DealerCode cannot be null or empty")
        @Pattern(regexp = DEALER_CODE_REGEX, message = "Please provide a valid dealer code")
        private String dealerCode;

        @Size(max = 50, message = "Created by must be up to 50 characters")
        private String createdBy;

        @Size(max = 50, message = "Updated by must be up to 50 characters")

        private String updatedBy;

        @Size(max = 10, message = "Status flag must be up to 10 characters")
        private String statusFlag;
    }


