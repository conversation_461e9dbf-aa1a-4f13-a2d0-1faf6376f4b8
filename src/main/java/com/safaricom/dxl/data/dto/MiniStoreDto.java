package com.safaricom.dxl.data.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.safaricom.dxl.utils.MsStarterVariables.DEALER_CODE_REGEX;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MiniStoreDto {
    @NotNull(message = "MiniStore Id can not be null")
    private String miniStoreId;
    @Pattern(regexp = DEALER_CODE_REGEX, message = "Please provide a valid dealer code")
    @NotNull(message = "DealerCode can not be null")
    private String dealerCode;
    @NotNull(message = "MiniStore Name can not be null")
    private String miniStoreName;

}
