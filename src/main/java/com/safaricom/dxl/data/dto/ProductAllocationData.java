package com.safaricom.dxl.data.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ProductAllocationData {
    private Long id;
    private String dealerCode;
    private int allocation;
    private String productCode;
    private String productName;
    private int ordered;
    private LocalDateTime created;
    private String createdBy;
    private LocalDateTime updatedOn;
    private LocalDate startDate;
    private LocalDate endDate;
    private int availableBalance;
    private int supplementaryAllocation;

    private List<ProductAllocationLogData> logData = new ArrayList<>();

}
