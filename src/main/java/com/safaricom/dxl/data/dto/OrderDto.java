package com.safaricom.dxl.data.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import static com.safaricom.dxl.utils.MsStarterVariables.INTEGER_REGEX;
import static com.safaricom.dxl.utils.MsStarterVariables.USERNAME_VALIDATION_REGEX;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class OrderDto {
    @Pattern(regexp = USERNAME_VALIDATION_REGEX, message = "Please provide a valid product code")
    @Valid
    private String productCode;
    @Pattern(regexp = INTEGER_REGEX, message = "Please provide a valid quantity")
    @Valid
    private String quantity;
    @Pattern(regexp = INTEGER_REGEX, message = "Please provide a valid miniStoreId")
    @Valid
    private String miniStoreId;
    @Pattern(regexp = INTEGER_REGEX, message = "Please provide a valid shipToId")
    @Valid
    private String shipToId;
}
