package com.safaricom.dxl.data.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
public class ProductAllocationLogData {
    private Long id;
    private LocalDateTime updatedOn;
    private String updatedBy;
    private int allocation;
    private Boolean isSupplementary = Boolean.FALSE;
    private String allocationFor;
    private LocalDate specifiedStartDate;
    private LocalDate specifiedEndDate;
}
