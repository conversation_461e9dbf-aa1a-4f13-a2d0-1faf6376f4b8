package com.safaricom.dxl.data.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class SaveOrderItemsDto {
    @NotNull(message = "Cart ids must not be null.")
    @Valid
    private List<OrderDto> orderItems;
    @Min(value = 1, message = "userId Value should not be less than 1")
    @Valid
    private long userId;
}
