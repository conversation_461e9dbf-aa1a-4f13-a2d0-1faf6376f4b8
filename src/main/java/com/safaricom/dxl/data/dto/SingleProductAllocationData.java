package com.safaricom.dxl.data.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class SingleProductAllocationData {
    private String dealerCode;
    private String productCode;
    private int newAllocation;
    private LocalDate startDate;
    private java.time.LocalDate endDate;
    private String productName;
    private String allocationFor;
    private Boolean isUpdate = Boolean.FALSE;
}
