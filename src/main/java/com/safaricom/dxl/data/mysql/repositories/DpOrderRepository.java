package com.safaricom.dxl.data.mysql.repositories;

import com.safaricom.dxl.data.mysql.entities.DpOrderEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

public interface DpOrderRepository extends ReactiveCrudRepository<DpOrderEntity, Integer> {
    @Query("select COALESCE(sum(doi.quantity), 0) from dp_orders dd " +
            "join dp_orders_items doi on  dd.orderId = doi.orderId " +
            "where dd.dealerCode = :dealerCode and dd.erpOrderNumber is not NULL and dd.orderedOn BETWEEN :start AND :end and doi.productCode = :productCode")
    Mono<Long> customByDealerCodeAndProductCodeAndStartDateAndEndDate(String dealerCode, String productCode, LocalDateTime start, LocalDateTime end);
}
