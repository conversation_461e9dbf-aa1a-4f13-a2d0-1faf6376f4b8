package com.safaricom.dxl.data.mysql.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("dp_orders_items")
public class DpOrderItemEntity {
    private int id;
    @Column("orderId")
    private int orderId;
    @Column("productCode")
    private String productCode;
    private int quantity;
}
