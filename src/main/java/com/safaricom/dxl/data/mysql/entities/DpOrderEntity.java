package com.safaricom.dxl.data.mysql.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("dp_orders")
public class DpOrderEntity {
    @Column("orderId")
    private int orderId;
    @Column("dealerCode")
    private String dealerCode;
    @Column("orderStatus")
    private String orderStatus;
    @Column("erpOrderNumber")
    private int erpOrderNumber;
    @Column("orderOn")
    private LocalDateTime orderOn;
    @Column("orderBy")
    private String orderBy;
}
