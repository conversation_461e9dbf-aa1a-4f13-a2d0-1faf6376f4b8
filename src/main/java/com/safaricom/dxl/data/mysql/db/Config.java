package com.safaricom.dxl.data.mysql.db;

import com.safaricom.dxl.config.PIIDataDecryption;
import io.asyncer.r2dbc.mysql.MySqlConnectionConfiguration;
import io.asyncer.r2dbc.mysql.MySqlConnectionFactory;
import io.r2dbc.pool.ConnectionPool;
import io.r2dbc.pool.ConnectionPoolConfiguration;
import io.r2dbc.spi.ConnectionFactory;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.r2dbc.core.DefaultReactiveDataAccessStrategy;
import org.springframework.data.r2dbc.core.R2dbcEntityOperations;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.r2dbc.dialect.MySqlDialect;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.core.DatabaseClient;

import java.time.Duration;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Configuration
@EnableR2dbcRepositories(
        basePackages = "com.safaricom.dxl.data.mysql.repositories"
        , entityOperationsRef = "dpTemplate"
)
public class Config {

    private final Properties properties;
    private final PIIDataDecryption piiDataDecryption;

    @Bean
    public ConnectionFactory dpPortalConnectionFactory() {
        var connectionFactory = MySqlConnectionFactory.from(MySqlConnectionConfiguration.builder()
                .host(piiDataDecryption.decrypt(properties.getHost()))
                .port(Integer.parseInt(piiDataDecryption.decrypt(properties.getPort())))
                .username(piiDataDecryption.decrypt(properties.getUsername()))
                .password(piiDataDecryption.decrypt(properties.getPassword()))
                .database(piiDataDecryption.decrypt(properties.getDatabase()))
                .build());
        var configuration = ConnectionPoolConfiguration.builder(connectionFactory)
                .initialSize(properties.getPoolInitialSize())
                .maxSize(properties.getPoolMaxSize())
                .maxIdleTime(Duration.ofMinutes(properties.getPoolMaxIdleTime()))
                .validationQuery(properties.getPoolValidationQuery())
                .build();
        return new ConnectionPool(configuration);
    }

    @Bean
    public R2dbcEntityOperations dpTemplate(@Qualifier("dpPortalConnectionFactory") ConnectionFactory dpPortalConnectionFactory) {

        DefaultReactiveDataAccessStrategy strategy = new DefaultReactiveDataAccessStrategy(MySqlDialect.INSTANCE);
        DatabaseClient databaseClient = DatabaseClient.builder()
                .connectionFactory(dpPortalConnectionFactory)
                .bindMarkers(MySqlDialect.INSTANCE.getBindMarkersFactory())
                .build();

        return new R2dbcEntityTemplate(databaseClient, strategy);
    }
}