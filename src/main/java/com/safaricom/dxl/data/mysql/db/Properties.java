package com.safaricom.dxl.data.mysql.db;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
/**
 * <AUTHOR>
 */

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dxl.mysql")
public class Properties {
    private Integer poolMaxSize;
    private Integer poolInitialSize;
    private Long poolMaxIdleTime;
    private String poolValidationQuery;

    private String host;
    private String port;
    private String database;
    private String schema;
    private String username;
    private String password;
}