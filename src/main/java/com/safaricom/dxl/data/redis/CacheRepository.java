package com.safaricom.dxl.data.redis;

import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Objects;


/**
 * <AUTHOR>
 */

@Repository
@RedisHash
public class CacheRepository {

    private final ReactiveValueOperations<String, Object> valueOperations;

    public CacheRepository(ReactiveRedisTemplate<String, Object> redisTemplate) {
        this.valueOperations = redisTemplate.opsForValue();
    }

    public Mono<Boolean> cacheData(String key, String value, Duration duration) {
        return valueOperations.set(key, Objects.requireNonNull(value), duration);
    }

    public Mono<String> getCachedData(String key) {
        return valueOperations.get(key).mapNotNull(Object::toString);
    }

}
