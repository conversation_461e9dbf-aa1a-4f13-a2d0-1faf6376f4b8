package com.safaricom.dxl.data.response;

import com.safaricom.dxl.data.postgres.entities.CartEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class CartResponse extends CartEntity {
    private String productDescription;
    private Long cartId;
    private int categoryId;
    private String categoryName;
    private String miniStoreName;

    public CartResponse(CartEntity cart, String productDescription, Long cartId, int categoryId, String categoryName, String miniStoreName) {
        super(cart.getId(), cart.getUserId(), cart.getOrderItemStatus(), cart.getProductCode(), cart.getPrice(), cart.getTax(), cart.getTotal(), cart.getQuantity(), cart.getMiniStoreId(), cart.getShipToId(), cart.isStockAvailable(), cart.getCreated(), cart.getUpdated());
        this.productDescription = productDescription;
        this.cartId = cartId;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.miniStoreName = miniStoreName;
    }
}
