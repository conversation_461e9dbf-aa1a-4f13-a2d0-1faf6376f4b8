package com.safaricom.dxl.data.response;

import com.safaricom.dxl.data.model.SavedOrderItem;
import com.safaricom.dxl.data.model.SavedOrderItemResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SavedOrderResponse {
    private String id;
    private long userId;
    private String dealerCode;
    private LocalDateTime created;
    private List<SavedOrderItemResponse> savedOrderItemResponses;
    private List<SavedOrderItem> savedOrderItems;
}
