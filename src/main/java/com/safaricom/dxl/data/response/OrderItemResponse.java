package com.safaricom.dxl.data.response;

import com.safaricom.dxl.data.postgres.entities.OrderItemsEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderItemResponse extends OrderItemsEntity {
    private String productDescription;
    private String categoryName;
    private String miniStoreName;
}
