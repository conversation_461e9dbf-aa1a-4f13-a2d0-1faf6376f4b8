package com.safaricom.dxl.data.xml;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.safaricom.dxl.data.response.OrderResponse;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@XmlRootElement(name = "Envelope" , namespace = "http://schemas.xmlsoap.org/soap/envelope/")
@ToString
public class CreateOrderResponse {
    @XmlElement(name = "Body", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
    @JacksonXmlProperty(localName = "Body")
    public BodyMainResponse bodyMainResponse;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class BodyMainResponse {
        @XmlElement(name = "Response", namespace = "http://xmlns.telco.com/ServiceCatalog/Business/CreateDealerSalesOrders_v1")
        @JacksonXmlProperty(localName = "Response")
        public Response response;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class Response {
        @XmlElement(name = "ResultCode")
        @JacksonXmlProperty(localName = "ResultCode")
        public String responseCode;
        @XmlElement(name = "OrderNumber")
        @JacksonXmlProperty(localName = "OrderNumber")
        public String orderNumber;
        @XmlElement(name = "ResultDesc")
        @JacksonXmlProperty(localName = "ResultDesc")
        public String responseMessage;

        public OrderResponse toOrderResponse(){
            return OrderResponse.builder()
                    .orderNumber(orderNumber)
                    .build();
        }
    }
}
