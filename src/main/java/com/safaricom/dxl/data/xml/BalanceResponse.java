package com.safaricom.dxl.data.xml;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@XmlRootElement(name = "Envelope" , namespace = "http://schemas.xmlsoap.org/soap/envelope/")
@ToString
public class BalanceResponse {
    @XmlElement(name = "Body", namespace = "http://schemas.xmlsoap.org/soap/envelope/")
    @JacksonXmlProperty(localName = "Body")
    public BodyMainResponse bodyMainResponse;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class BodyMainResponse {
        @XmlElement(name = "OutputParameters", namespace = "http://xmlns.oracle.com/pcbpel/adapter/db/sp/GetItemPricing")
        @JacksonXmlProperty(localName = "OutputParameters")
        public Response response;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class Response {
        @XmlElement(name = "Header")
        @JacksonXmlProperty(localName = "Header")
        public Header header;
        @XmlElement(name = "Body")
        @JacksonXmlProperty(localName = "Body")
        public Message message;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class Header {
        @XmlElement(name = "RespCode")
        @JacksonXmlProperty(localName = "RespCode")
        public Header responseCode;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class Message {
        @XmlElement(name = "Resultsets")
        @JacksonXmlProperty(localName = "Resultsets")
        public ResultSets resultSets;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class ResultSets {
        @XmlElement(name = "ResultSet")
        @JacksonXmlProperty(localName = "ResultSet")
        public ResultSet resultSet;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class ResultSet {
        @XmlElement(name = "UnclearedEffectsRecord")
        @JacksonXmlProperty(localName = "UnclearedEffectsRecord")
        public UnclearedEffectsRecord unclearedEffectsRecord;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class UnclearedEffectsRecord {
        @XmlElement(name = "ACCOUNT_BALANCE")
        @JacksonXmlProperty(localName = "ACCOUNT_BALANCE")
        public double accountBalance;
        @XmlElement(name = "UNCLEARED_EFFECTS")
        @JacksonXmlProperty(localName = "UNCLEARED_EFFECTS")
        public double unclearedEffects;
        @XmlElement(name = "AVAILABLE_BALANCE")
        @JacksonXmlProperty(localName = "AVAILABLE_BALANCE")
        public double availableBalance;
    }
}
