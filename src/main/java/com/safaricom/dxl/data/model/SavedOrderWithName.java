package com.safaricom.dxl.data.model;

import com.safaricom.dxl.data.postgres.entities.SavedOrderEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SavedOrderWithName extends SavedOrderEntity {
    private String firstName;

    public SavedOrderEntity toSavedOrder(){
        return new SavedOrderEntity(
                getId(),
                getUserId(),
                getDealerCode(),
                getCreated(),
                getUpdated(),
                false
        );
    }

    public SavedOrderWithName(String id, long userId, String dealerCode, LocalDateTime created, LocalDateTime updated, boolean newEntry, String firstName) {
        super(id, userId, dealerCode, created, updated, newEntry);
        this.firstName = firstName;
    }
}
