package com.safaricom.dxl.data.model;

import com.safaricom.dxl.data.enums.OrderStatus;
import com.safaricom.dxl.data.response.OrderItemResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Order {
    private String firstName;
    private String email;
    private String erpOrderId;
    private String dealerCode;
    private OrderStatus orderStatus;
    private String invoiceNumber;
    private String invoiceId;
    private String cuNumber;
    private LocalDateTime created;
    private List<OrderItemResponse> orderList;
}
