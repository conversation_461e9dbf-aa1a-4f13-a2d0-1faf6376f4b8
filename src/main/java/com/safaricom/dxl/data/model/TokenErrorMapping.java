package com.safaricom.dxl.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class TokenErrorMapping {
    private String errorMessage;
    private String email;
    private String role;
    private String phone;
    private String firstName;

    public TokenErrorMapping(String errorMessage, String email, String role, String phone) {
        this.errorMessage = errorMessage;
        this.email = email;
        this.role = role;
        this.phone = phone;
    }

    public TokenErrorMapping(String errorMessage, String email, String role) {
        this.errorMessage = errorMessage;
        this.email = email;
        this.role = role;
    }

    public TokenErrorMapping(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
