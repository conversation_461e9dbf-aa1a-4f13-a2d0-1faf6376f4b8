package com.safaricom.dxl.data.model;

import lombok.*;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@ToString
@Builder
public class DataStreaming implements Serializable {
    private String sourceSystem;
    private String serviceName;
    private String resource;
    private String dealerCode;
    private String dealerName;
    private String role;
    private long timeStamp;
    private String transactionId;
    private String responseCode;
    private String responseMessage;
    private boolean transactional;
    private double transactionValue;
    private long responseTime;
    private String extraInfo;
    private String username;

    public DataStreaming(String dealerCode, String dealerName, String role) {
        this.dealerCode = dealerCode;
        this.dealerName = dealerName;
        this.role = role;
    }
}
