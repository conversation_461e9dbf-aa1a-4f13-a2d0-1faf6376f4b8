package com.safaricom.dxl.data.model;

import com.safaricom.dxl.data.enums.OrderStatus;
import com.safaricom.dxl.data.postgres.entities.OrderEntity;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString
public class OrderDetail extends OrderEntity {
    private String firstName;
    private String email;

    public OrderDetail(String id, long userId, String erpOrderId, String dealerCode, Set<Long> orderItemIds,
                       OrderStatus orderStatus, long lineItemId, String invoiceNumber, String invoiceId,
                       String cuNumber, LocalDateTime created, LocalDateTime updated,
                       String firstName, String email) {
        super(id, userId, erpOrderId, dealerCode, orderItemIds, orderStatus, lineItemId, invoiceNumber, invoiceId, cuNumber, created, updated, false);
        this.firstName = firstName;
        this.email = email;
    }

    public Order toOrder() {
        return new Order(
                firstName,
                email,
                getErpOrderId(),
                getDealerCode(),
                getOrderStatus(),
                getInvoiceNumber(),
                getInvoiceId(),
                getCuNumber(),
                getCreated(),
                null
        );
    }
}
