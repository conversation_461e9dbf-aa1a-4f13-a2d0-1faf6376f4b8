package com.safaricom.dxl.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SavedOrderItemResponse {
    private String productCode;
    private int miniStoreId;
    private int shipToId;
    private int quantity;
    private String productDescription;
    private double productPrice;
    private double productListPrice;
    private double tax;
    private double total;
    private String taxCode;
    private String miniStoreName;
    private String address;
}
