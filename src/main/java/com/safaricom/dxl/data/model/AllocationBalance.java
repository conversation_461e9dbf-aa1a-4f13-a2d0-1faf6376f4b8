package com.safaricom.dxl.data.model;

import com.safaricom.dxl.data.dto.ProductAllocationLogData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
public class AllocationBalance {
    private String dealerCode;
    private int allocation;
    private int additionalAllocation;
    private String productCode;
    private int ordered;
    private int availableBalance;

    List<ProductAllocationLogData> logData;
}
