package com.safaricom.dxl.data.model;

import com.safaricom.dxl.data.postgres.entities.SavedOrderItemEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SavedOrderItem extends SavedOrderItemEntity {
    private String productDescription;
    private double productPrice;
    private double productListPrice;
    private double tax;
    private double total;
    private String taxCode;
    private String miniStoreName;
    private String address;

}
