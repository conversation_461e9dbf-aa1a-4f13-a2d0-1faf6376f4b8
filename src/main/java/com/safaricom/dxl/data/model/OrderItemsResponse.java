package com.safaricom.dxl.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Builder
@Data
@AllArgsConstructor
public class OrderItemsResponse {
    private Long id;
    private Long userId;
    private String productCode;
    private double price;
    private double tax;
    private double total;
    private int quantity;
    private int miniStoreId;
    private int shipToId;
    private LocalDateTime created;
    private String productDescription;
}
