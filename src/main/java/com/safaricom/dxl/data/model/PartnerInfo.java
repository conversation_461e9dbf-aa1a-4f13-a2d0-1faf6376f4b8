package com.safaricom.dxl.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PartnerInfo {
    long startTime;
    String dealerCode;
    String dealerName;
    String role;
    Double transactionValue;
    String reportName;
    String additionalData;
    String email;
    String phone;


    public PartnerInfo(long startTime, String dealerCode) {
        this.startTime = startTime;
        this.dealerCode = dealerCode;
    }

    public PartnerInfo(long startTime, String dealerCode, String reportName) {
        this.startTime = startTime;
        this.dealerCode = dealerCode;
        this.reportName = reportName;
    }

    public PartnerInfo(long startTime, String dealerCode, String reportName, String email) {
        this.startTime = startTime;
        this.dealerCode = dealerCode;
        this.reportName = reportName;
        this.email = email;
    }
}
