package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_products")
public class Product {
    @Id
    private Long id;
    private int categoryId;
    private String productCode;
    private String productDescription;
    private double productPrice;
    private double productListPrice;
    private double tax;
    private double total;
    private LocalDateTime created;
    private LocalDateTime updated;
    private String taxCode;
}
