package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.LogsAllocationEntity;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface LogsAllocationRepository extends R2dbcRepository<LogsAllocationEntity, Long> {
    Flux<LogsAllocationEntity> findByAllocationId(final Long allocationId);

    Flux<LogsAllocationEntity> findByAllocationIdAndAllocationFor(final Long allocationId, final String allocationFor);

}
