package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.dto.MiniStoreDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("po_mini_store_mapping")
public class MiniStoreMappingEntity {
    @Id
    private Long id;
    private int miniStoreId;
    private String dealerCode;
    private String miniStoreName;
    private String createdBy;
    private LocalDateTime created;
    private String updatedBy;
    private LocalDateTime updated;

    public static MiniStoreMappingEntity of(MiniStoreDto dto, String identity) {
        return new MiniStoreMappingEntity(
                null,
                Integer.parseInt(dto.getMiniStoreId()),
                dto.getDealerCode(),
                identity,
                dto.getMiniStoreName(),
                LocalDateTime.now(),
                null,
                null
        );
    }
}

