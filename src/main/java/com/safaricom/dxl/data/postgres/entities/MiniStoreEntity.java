package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("po_mini_store")
public class MiniStoreEntity {
    @Id
    private Long id;
    private int miniStoreId;
    private String miniStoreCode;
    private String miniStoreName;

}
