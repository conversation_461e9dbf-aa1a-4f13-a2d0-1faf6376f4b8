package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "po_allocation_logs")
public class LogsAllocationEntity {
    @Id
    private Long id;

    private String dealerCode;
    private String productCode;
    private String productName;
    private LocalDateTime updatedOn;
    private String updatedBy;
    private int allocation;

    @Column("allocation_id")
    private Long allocationId;
    private Boolean isSupplementary = Boolean.FALSE;
    private String allocationFor;
    //specified duration
    private LocalDate specifiedStartDate;
    private LocalDate specifiedEndDate;
}