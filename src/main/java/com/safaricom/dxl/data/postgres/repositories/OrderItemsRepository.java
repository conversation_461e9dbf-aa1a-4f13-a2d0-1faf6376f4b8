package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.enums.OrderItemStatus;
import com.safaricom.dxl.data.postgres.entities.OrderItemsEntity;
import com.safaricom.dxl.data.response.OrderItemResponse;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Set;

@Repository
public interface OrderItemsRepository extends R2dbcRepository<OrderItemsEntity, Long> {
    Mono<OrderItemsEntity> findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(Long userId, String productCode, int miniStoreId, OrderItemStatus orderItemStatus);

    @Query("SELECT c.id, c.user_id, c.order_item_status, pc.category_id, pc.category_name, c.product_code, p.product_description,c.price, c.tax, c.total, c.quantity, c.mini_store_id, m.mini_store_name, c.ship_to_id, c.created, c.updated " +
            "FROM dealer.po_order_item c " +
            "join dealer.po_products p on c.product_code = p.product_code " +
            "join dealer.po_category pc on p.category_id = pc.category_id " +
            "join dealer.po_mini_store m on c.mini_store_id = m.mini_store_id " +
            "where c.id in (:orderItemIds)")
    Flux<OrderItemResponse> customFindByOrderItemIds(Set<Long> orderItemIds);

    @Query("select COALESCE(sum(poi.quantity), 0) as ordered from dealer.po_order po " +
            "join dealer.po_order_item poi on po.id = poi.order_id " +
            "where po.dealer_code = :dealerCode and po.erp_order_id is not NULL and po.created BETWEEN :start AND :end and poi.product_code = :productCode")
    Mono<Long> customByDealerCodeAndProductCodeAndStartDateAndEndDate(String dealerCode, String productCode, LocalDateTime start, LocalDateTime end);
}
