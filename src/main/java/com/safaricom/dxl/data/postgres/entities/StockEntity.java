package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("po_stock")
public class StockEntity {
    @Id
    private Long id;
    private int miniStoreId;
    private String productCode;
    private int quantity;
    private LocalDateTime created;
    private LocalDateTime updated;
}
