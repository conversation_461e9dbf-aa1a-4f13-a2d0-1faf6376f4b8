package com.safaricom.dxl.data.postgres.db;

import com.safaricom.dxl.config.PIIDataDecryption;
import io.r2dbc.pool.ConnectionPool;
import io.r2dbc.pool.ConnectionPoolConfiguration;
import io.r2dbc.postgresql.PostgresqlConnectionConfiguration;
import io.r2dbc.postgresql.PostgresqlConnectionFactory;
import io.r2dbc.spi.ConnectionFactory;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.r2dbc.config.AbstractR2dbcConfiguration;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;

import java.time.Duration;
/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Configuration
@EnableR2dbcRepositories(
        basePackages = "com.safaricom.dxl.data.postgres.repositories"
)
public class PostgresConfig extends AbstractR2dbcConfiguration {

    private final PostgresProperties properties;
    private final PIIDataDecryption piiDataDecryption;

    @Bean
    @Override
    @Qualifier(value = "portalConnectionFactory")
    public ConnectionFactory connectionFactory() {
        var connectionFactory = new PostgresqlConnectionFactory(PostgresqlConnectionConfiguration.builder()
                .host(piiDataDecryption.decrypt(properties.getHost()))
                .port(Integer.parseInt(piiDataDecryption.decrypt(properties.getPort())))
                .username(piiDataDecryption.decrypt(properties.getUsername()))
                .password(piiDataDecryption.decrypt(properties.getPassword()))
                .database(piiDataDecryption.decrypt(properties.getDatabase()))
                .schema(piiDataDecryption.decrypt(properties.getSchema()))
                .build());
        var configuration = ConnectionPoolConfiguration.builder(connectionFactory)
                .initialSize(properties.getPoolInitialSize())
                .maxSize(properties.getPoolMaxSize())
                .maxIdleTime(Duration.ofMinutes(properties.getPoolMaxIdleTime()))
                .validationQuery(properties.getPoolValidationQuery())
                .build();
        return new ConnectionPool(configuration);
    }
}