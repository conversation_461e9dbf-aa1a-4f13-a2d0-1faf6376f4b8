package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_category")
public class ProductCategory {
    @Id
    private Long id;
    private int categoryId;
    private String categoryName;
    private String categoryDescription;
    private LocalDateTime created;
    private LocalDateTime updated;
}