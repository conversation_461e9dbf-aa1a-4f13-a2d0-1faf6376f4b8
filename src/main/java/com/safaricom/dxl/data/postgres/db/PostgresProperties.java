package com.safaricom.dxl.data.postgres.db;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
/**
 * <AUTHOR>
 */

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dxl.r2dbc")
public class PostgresProperties {
    private Integer poolMaxSize;
    private Integer poolInitialSize;
    private Long poolMaxIdleTime;
    private String poolValidationQuery;

    private String host;
    private String port;
    private String database;
    private String schema;
    private String username;
    private String password;

}