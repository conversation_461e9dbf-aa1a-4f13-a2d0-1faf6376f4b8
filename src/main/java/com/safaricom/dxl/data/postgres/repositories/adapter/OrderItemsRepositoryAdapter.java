package com.safaricom.dxl.data.postgres.repositories.adapter;

import com.safaricom.dxl.data.enums.OrderItemStatus;
import com.safaricom.dxl.data.postgres.entities.OrderItemsEntity;
import com.safaricom.dxl.data.postgres.repositories.OrderItemsRepositoryPort;
import lombok.AllArgsConstructor;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.Set;

import static org.springframework.data.relational.core.query.Criteria.where;

@Component
@AllArgsConstructor
public class OrderItemsRepositoryAdapter implements OrderItemsRepositoryPort {
    private final R2dbcEntityTemplate template;

    public Mono<Long> updateOrderItemsStatus(Collection<Long> ids, OrderItemStatus status){
        return this.template.update(OrderItemsEntity.class)
                .matching(Query.query(where("id").in(ids)))
                .apply(Update.update("order_item_status", status.value));
    }

    @Override
    public Mono<Long> updateOrderId(Set<Long> itemIds, String orderIdNew) {
        return this.template.update(OrderItemsEntity.class)
                .matching(Query.query(where("id").in(itemIds)))
                .apply(Update.update("order_id", orderIdNew));
    }
}