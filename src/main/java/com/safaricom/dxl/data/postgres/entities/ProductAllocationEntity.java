package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.dto.ProductAllocationData;
import com.safaricom.dxl.data.dto.ProductAllocationLogData;
import com.safaricom.dxl.data.model.AllocationBalance;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.safaricom.dxl.utils.Utilities.getProductAllocationLogData;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_allocations")
public class ProductAllocationEntity {
    @Id
    private Long id;
    private String dealerCode;
    private int allocation;
    private int additionalAllocation;
    private String productCode;
    private String productName;
    private int ordered;
    private LocalDateTime created;
    private String createdBy;
    private LocalDateTime updatedOn;
    private LocalDate startDate;
    private LocalDate endDate;
    private String updatedBy;
    private boolean updatedStatus;
    private String category;

    public AllocationBalance toBalance() {
        return AllocationBalance.builder().dealerCode(dealerCode)
                .additionalAllocation(additionalAllocation)
                .allocation(allocation)
                .productCode(productCode)
                .ordered(ordered).build();
    }

    public ProductAllocationData toData(List<LogsAllocationEntity> logs) {
        ProductAllocationData data = new ProductAllocationData();
        data.setId(this.id);
        data.setDealerCode(this.dealerCode);
        data.setAvailableBalance(this.allocation);
        data.setProductCode(this.productCode);
        data.setProductName(this.productName);
        data.setOrdered(this.ordered);
        data.setCreated(this.created);
        data.setCreatedBy(this.createdBy);
        data.setUpdatedOn(this.updatedOn);
        data.setStartDate(this.startDate);
        data.setEndDate(this.endDate);
        data.setAllocation((this.allocation - this.additionalAllocation));
        data.setSupplementaryAllocation(this.additionalAllocation);

        if (!logs.isEmpty()) {
            List<ProductAllocationLogData> logDataList = new ArrayList<>();


            for (LogsAllocationEntity log : logs) {
                ProductAllocationLogData logData = getProductAllocationLogData(log);

                logDataList.add(logData);
            }

            data.setLogData(logDataList);
        }
        return data;
    }
}
