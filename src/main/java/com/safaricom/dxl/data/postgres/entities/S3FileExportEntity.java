package com.safaricom.dxl.data.postgres.entities;


import com.safaricom.dxl.data.model.S3FileDetails;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.Persistable;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

import static com.safaricom.dxl.utils.Utilities.localDateTime;
import static com.safaricom.dxl.utils.Utilities.s3FileId;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.TRUE;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("s3_excel_export")
@ToString
public class S3FileExportEntity implements Persistable<String> {
    @Id
    private String id;
    private String dealerCode;
    private String reportName;
    private boolean generated;
    private LocalDateTime created;
    private LocalDateTime updated;

    public S3FileExportEntity(String id, String dealerCode, String reportName, boolean generated, LocalDateTime created, LocalDateTime updated) {
        this.id = id;
        this.dealerCode = dealerCode;
        this.reportName = reportName;
        this.generated = generated;
        this.created = created;
        this.updated = updated;
    }

    public S3FileExportEntity(String id, String dealerCode, String reportName, boolean generated, LocalDateTime updated) {
        this.id = id;
        this.dealerCode = dealerCode;
        this.reportName = reportName;
        this.generated = generated;
        this.updated = updated;
    }

    public S3FileDetails tos3FileDetails(){
        return S3FileDetails.builder()
                .reportId(id)
                .dealerCode(dealerCode)
                .generated(generated)
                .build();
    }

    @Transient
    private boolean newEntry;

    public static S3FileExportEntity of(String id, String code, String reportName) {
        return new S3FileExportEntity(id, code, reportName, FALSE, localDateTime(), null).setAsNew();
    }

    public static S3FileExportEntity of(String code, String start, String end, String reportName) {
        return new S3FileExportEntity(s3FileId(code, start, end, reportName), code, reportName, TRUE, localDateTime());
    }

    public S3FileExportEntity setAsNew() {
        this.newEntry = true;
        return this;
    }

    @Override
    @Transient
    public boolean isNew() {
        return this.newEntry || id == null;
    }
}
