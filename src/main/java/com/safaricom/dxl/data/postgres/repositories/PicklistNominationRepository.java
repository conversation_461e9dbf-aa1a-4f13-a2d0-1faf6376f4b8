package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.PicklistNominationEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


@Repository
public interface PicklistNominationRepository extends R2dbcRepository<PicklistNominationEntity, Long> {

    @Query("SELECT * FROM dealer.po_picklist_nomination WHERE status_flag = 'ACTIVE' AND dealer_code = :dealerCode ORDER BY created DESC")
    Flux<PicklistNominationEntity> findActiveNominationsByDealerCode(String dealerCode, Pageable pageable);

    Mono<PicklistNominationEntity> findByDocumentNumberAndMiniStoreName(String documentNumber, String miniStoreName);


}
