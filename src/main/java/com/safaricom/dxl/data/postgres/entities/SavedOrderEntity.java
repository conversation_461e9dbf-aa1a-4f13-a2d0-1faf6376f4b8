package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.model.SavedOrderItem;
import com.safaricom.dxl.data.model.SavedOrderItemResponse;
import com.safaricom.dxl.data.response.SavedOrderResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.Persistable;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.List;

import static com.safaricom.dxl.utils.Utilities.localDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_saved_order")
public class SavedOrderEntity implements Persistable<String> {
    @Id
    private String id;
    private long userId;
    private String dealerCode;
    private LocalDateTime created;
    private LocalDateTime updated;
    @Transient
    private boolean newEntry;

    public static SavedOrderEntity of(long userId, String orderIdentifier, String dealerCode) {
        return new SavedOrderEntity(
                orderIdentifier,
                userId,
                dealerCode,
                localDateTime(),
                null,
                true
        );
    }

    public SavedOrderEntity setAsNew() {
        this.newEntry = true;
        return this;
    }

    public SavedOrderEntity setAsUpdate() {
        this.newEntry = false;
        return this;
    }

    @Override
    @Transient
    public boolean isNew() {
        return this.newEntry || id == null;
    }

    public SavedOrderResponse toSavedOrderResponse(List<SavedOrderItemResponse> savedOrderItemResponses) {
        return SavedOrderResponse.builder()
                .id(id)
                .userId(userId)
                .dealerCode(dealerCode)
                .created(created)
                .savedOrderItemResponses(savedOrderItemResponses)
                .build();
    }

    public SavedOrderResponse toSavedOrderItems(List<SavedOrderItem> savedOrderItems) {
        return SavedOrderResponse.builder()
                .id(id)
                .userId(userId)
                .dealerCode(dealerCode)
                .created(created)
                .savedOrderItems(savedOrderItems)
                .build();
    }
}
