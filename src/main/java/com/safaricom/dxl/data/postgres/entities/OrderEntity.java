package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.enums.OrderStatus;
import com.safaricom.dxl.data.model.Order;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.Persistable;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.Set;

import static com.safaricom.dxl.utils.Utilities.localDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_order")
public class OrderEntity implements Persistable<String> {
    @Id
    private String id;
    private long userId;
    private String erpOrderId;
    private String dealerCode;
    private Set<Long> orderItemIds;
    private OrderStatus orderStatus;
    private long lineItemId;
    private String invoiceNumber;
    private String invoiceId;
    private String cuNumber;
    private LocalDateTime created;
    private LocalDateTime updated;

    public static OrderEntity of(Set<Long> itemIds, String dealerCode, long userId, long lineItemId, String orderId) {
        return new OrderEntity(
                orderId,
                userId,
                null,
                dealerCode,
                itemIds,
                OrderStatus.CREATED,
                lineItemId,
                null,
                null,
                null,
                localDateTime(),
                null,
                true
        );
    }

    public Order toOrderDetails(String firstName, String email){
        return Order.builder()
                .firstName(firstName)
                .email(email)
                .erpOrderId(erpOrderId)
                .dealerCode(dealerCode)
                .orderStatus(orderStatus)
                .invoiceNumber(invoiceNumber)
                .invoiceId(invoiceId)
                .cuNumber(cuNumber)
                .created(created)
                .build();
    }

    public OrderEntity setAsNew() {
        this.newEntry = true;
        return this;
    }

    public OrderEntity setAsUpdate() {
        this.newEntry = false;
        return this;
    }

    @Transient
    private boolean newEntry;

    @Override
    @Transient
    public boolean isNew() {
        return this.newEntry || id == null;
    }
}
