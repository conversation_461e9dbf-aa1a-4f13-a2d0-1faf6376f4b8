package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.model.OrderDetail;
import com.safaricom.dxl.data.postgres.entities.OrderEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

@Repository
public interface OrderRepository extends R2dbcRepository<OrderEntity, String> {
    @Query("SELECT o.id, o.user_id, o.erp_order_id, o.dealer_code, o.order_item_ids, o.order_status, o.line_item_id, o.invoice_number, o.invoice_id, o.cu_number, o.created, o.updated, u.first_name, u.email " +
            "FROM dealer.po_order o " +
            "JOIN dealer.pp_users u ON u.id = o.user_id " +
            "WHERE o.dealer_code = :dealerCode and o.created between :from and :to order by created DESC offset :pageNo limit :pageSize")
    Flux<OrderDetail> customByDealerCode(String dealerCode, LocalDateTime from, LocalDateTime to, int pageNo, int pageSize);

    @Query("SELECT count(*) FROM dealer.po_order o " +
            "JOIN dealer.pp_users u ON u.id = o.user_id " +
            "WHERE o.dealer_code = :dealerCode and o.created between :from and :to")
    Mono<Long> customCountByDealerCode(String dealerCode, LocalDateTime from, LocalDateTime to);

    @Query("SELECT o.id, o.user_id, o.erp_order_id, o.dealer_code, o.order_item_ids, o.order_status, o.line_item_id, o.invoice_number, o.invoice_id, o.cu_number, o.created, o.updated, u.first_name, u.email " +
            "FROM dealer.po_order o " +
            "JOIN dealer.pp_users u ON u.id = o.user_id " +
            "WHERE o.dealer_code = :dealerCode and o.erp_order_id = :orderNumber")
    Mono<OrderDetail> customByErpOrderIdAndDealerCode(String orderNumber, String dealerCode);

    Flux<OrderEntity> findByDealerCodeAndCreatedBetweenOrderByCreatedDesc(String dealerCode, LocalDateTime toFormattedDate, LocalDateTime toFormattedDate1);
}
