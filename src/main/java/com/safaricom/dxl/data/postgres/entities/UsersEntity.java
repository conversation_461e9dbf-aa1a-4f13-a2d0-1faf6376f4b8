package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("pp_users")
public class UsersEntity {
    @Id
    private Long id;
    private String idType;
    private String registrationDocId;
    private String registrationStatus;
    private String registrationRemarks;
    private String firstName;
    private String lastName;
    private String phone;
    private String email;
    private String role;
    private Object clusters;
    private Object territory;
    private Object region;
    private Set<String> codes;
    private boolean isEnabled;
    private LocalDateTime created;
    private LocalDateTime updated;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime loginTime;
    private String sourceSystem;
}
