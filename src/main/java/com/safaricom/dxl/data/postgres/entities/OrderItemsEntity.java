package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.enums.OrderItemStatus;
import com.safaricom.dxl.data.model.OrderRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

import static com.safaricom.dxl.utils.Utilities.localDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_order_item")
public class OrderItemsEntity {
    @Id
    private Long id;
    private String orderId;
    private Long userId;
    private OrderItemStatus orderItemStatus;
    private String productCode;
    private double price;
    private double tax;
    private double total;
    private int quantity;
    private int miniStoreId;
    private int shipToId;
    private LocalDateTime created;
    private LocalDateTime updated;

    public static OrderItemsEntity of(CartEntity cart, String orderId) {
        return new OrderItemsEntity(
                null,
                orderId,
                cart.getUserId(),
                OrderItemStatus.PENDING,
                cart.getProductCode(),
                cart.getPrice(),
                cart.getTax(),
                cart.getTotal(),
                cart.getQuantity(),
                cart.getMiniStoreId(),
                cart.getShipToId(),
                localDateTime(),
                cart.getUpdated()
        );
    }

    public OrderRequest toOrderRequest(){
        return OrderRequest.builder()
                .productCode(productCode)
                .quantity(quantity)
                .miniStoreId(miniStoreId)
                .shipToId(shipToId)
                .build();
    }
}
