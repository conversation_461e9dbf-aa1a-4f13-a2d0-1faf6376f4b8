package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.StockEntity;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface StockRepository extends R2dbcRepository<StockEntity, Long> {
    Mono<StockEntity> findByMiniStoreIdAndProductCode(int miniStoreId, String productCode);

    Mono<StockEntity> findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(int miniStoreId, String productCode, int quantity);
}
