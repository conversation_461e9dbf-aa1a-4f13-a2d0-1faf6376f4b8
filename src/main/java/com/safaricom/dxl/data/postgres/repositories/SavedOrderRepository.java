package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.model.SavedOrderItem;
import com.safaricom.dxl.data.model.SavedOrderWithName;
import com.safaricom.dxl.data.postgres.entities.SavedOrderEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface SavedOrderRepository extends ReactiveCrudRepository<SavedOrderEntity, String> {

    @Query("SELECT so.id, so.user_id, so.order_identifier, so.dealer_code, so.product_code, so.mini_store_id, so.ship_to_id, so.quantity, so.created, " +
            "p.product_price, p.tax, p.tax_code, m.mini_store_name, st.address, p.total " +
            "FROM dealer.po_saved_order so " +
            "JOIN dealer.po_products p ON so.product_code = p.product_code " +
            "JOIN dealer.po_mini_store m ON so.mini_store_id = m.mini_store_id " +
            "JOIN dealer.po_ship_to st ON so.ship_to_id = st.ship_to_id " +
            "WHERE so.order_identifier = :orderIdentifier")
    Flux<SavedOrderItem> customByOrderIdentifier(String orderIdentifier);

    Flux<SavedOrderEntity> findByUserId(String userId, Pageable pageable);

    Mono<Long> countByUserId(String userId);

    @Query(value = "SELECT " +
            "order_identifier, " +
            "JSON_AGG(JSON_BUILD_OBJECT('product_code', product_code, 'mini_store_id', mini_store_id, 'ship_to_id', ship_to_id, 'quantity', quantity, 'created', created)) AS order_items " +
            "FROM dealer.po_saved_order " +
            "WHERE user_id = :userId " +
            "GROUP BY order_identifier " +
            "OFFSET :pageNo LIMIT :pageSize")
    Flux<SavedOrderWithName> customByUserId(String userId, int pageNo, int pageSize);

    Flux<SavedOrderEntity> findByDealerCode(String dealerCode, Pageable pageable);

    @Query("SELECT so.id, so.user_id, u.first_name, so.dealer_code, so.created, so.updated FROM dealer.po_saved_order so " +
            "JOIN dealer.pp_users u ON so.user_id = u.id " +
            "WHERE so.dealer_code = :dealerCode OFFSET :pageNo LIMIT :pageSize")
    Flux<SavedOrderWithName> findByDealerCode(String dealerCode, int pageNo, int pageSize);

    Mono<Long> countByDealerCode(String dealerCode);
}
