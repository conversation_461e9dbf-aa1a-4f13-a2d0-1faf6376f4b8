package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.ProductCategory;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface ProductCategoryRepository extends R2dbcRepository<ProductCategory, Long> {
    Flux<ProductCategory> findByCategoryName(String categoryName);
}
