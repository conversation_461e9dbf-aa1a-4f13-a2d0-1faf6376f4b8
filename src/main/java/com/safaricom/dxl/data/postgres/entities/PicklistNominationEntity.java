package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.dto.PicklistNominationDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "po_picklist_nomination")
public class PicklistNominationEntity {
    @Id
    private Long id;
    private String documentNumber;
    private String firstName;
    private String middleName;
    private String lastName;
    private String miniStoreName;
    private String documentType;
    private String phoneNumber;
    private String dealerCode;
    private LocalDateTime created;
    private String createdBy;
    private LocalDateTime updated;
    private String updatedBy;
    private String statusFlag;

    public PicklistNominationDto toBalance() {
        return PicklistNominationDto.builder()
                .documentNumber(documentNumber)
                .firstName(firstName)
                .middleName(middleName)
                .lastName(lastName)
                .miniStoreName(miniStoreName)
                .documentType(documentType)
                .phoneNumber(phoneNumber)
                .dealerCode(dealerCode)
                .createdBy(createdBy)
                .updatedBy(updatedBy)
                .statusFlag(statusFlag)
                .build();
    }
}

