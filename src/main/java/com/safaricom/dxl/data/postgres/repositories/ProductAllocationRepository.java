package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.ProductAllocationEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

@Repository
public interface ProductAllocationRepository extends R2dbcRepository<ProductAllocationEntity, Long> {

    Flux<ProductAllocationEntity> findByProductCode(String productCode);


    @Query("SELECT * FROM po_allocations " +
            "WHERE dealer_code = :dealerCode " +
            "AND :date BETWEEN start_date AND end_date")
    Flux<ProductAllocationEntity> findProductAllocationWithDate(@Param("dealerCode") String dealerCode, @Param("date") LocalDate date);

    @Query("select * from po_allocations where dealer_code = :dealerCode and product_code = :productCode and start_date <= :currentDate and end_date >= :currentDate1")
    Mono<ProductAllocationEntity> findByDealerCodeAndProductCodeAndStartDateLessThanEqualAndEndDateGreaterThanEqual(String dealerCode, String productCode, LocalDate currentDate, LocalDate currentDate1);

    @Query("""
                SELECT * 
                FROM po_allocations 
                WHERE dealer_code = :dealerCode
                  AND product_code = :productCode
                  AND start_date <= :endDate
                  AND end_date >= :startDate
            """)
    Mono<ProductAllocationEntity> findProductAllocationWithDates(
            String dealerCode,
            String productCode,
            LocalDate startDate,
            LocalDate endDate
    );

    @Query("select * from po_allocations where dealer_code = :dealerCode and product_code = :productCode and start_date <= :currentDate and end_date >= :currentDate")
    Mono<ProductAllocationEntity> customByDealerCodeAndProductCodeAndDate(String dealerCode, String productCode, LocalDate currentDate);
}