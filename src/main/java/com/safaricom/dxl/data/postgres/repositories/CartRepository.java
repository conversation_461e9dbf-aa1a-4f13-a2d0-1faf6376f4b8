package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.CartEntity;
import com.safaricom.dxl.data.response.CartResponse;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface CartRepository extends R2dbcRepository<CartEntity, Long> {

    Mono<CartEntity> findByUserIdAndProductCode(long userId, String productCode);

    @Query("SELECT c.id as cart_id, c.user_id, c.order_item_status, pc.category_id, pc.category_name, c.product_code, p.product_description, c.price, c.tax, c.total, c.quantity, c.mini_store_id, m.mini_store_name, c.ship_to_id, c.stock_available, c.created, c.updated " +
            "FROM dealer.po_cart_item c " +
            "join dealer.po_products p on c.product_code = p.product_code " +
            "join dealer.po_category pc on p.category_id = pc.category_id " +
            "join dealer.po_mini_store m on c.mini_store_id = m.mini_store_id " +
            "where c.user_id = :userId")
    Flux<CartResponse> customFindByUserId(Long userId);
}
