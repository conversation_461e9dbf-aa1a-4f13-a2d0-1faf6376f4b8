package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.dto.OrderDto;
import com.safaricom.dxl.data.model.SavedOrderItem;
import com.safaricom.dxl.data.model.SavedOrderItemResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_saved_order_item")
public class SavedOrderItemEntity {
    @Id
    private Long id;
    private String orderIdentifierId;
    private String productCode;
    private int miniStoreId;
    private int shipToId;
    private int quantity;

    public static SavedOrderItemEntity of(OrderDto dto, String orderIdentifier) {
        return new SavedOrderItemEntity(
                null,
                orderIdentifier,
                dto.getProductCode(),
                Integer.parseInt(dto.getMiniStoreId()),
                Integer.parseInt(dto.getShipToId()),
                Integer.parseInt(dto.getQuantity())
        );
    }

    public SavedOrderItemResponse toSavedOrderItemResponse(SavedOrderItem details){
        return SavedOrderItemResponse.builder()
                .productCode(productCode)
                .productDescription(details.getProductDescription())
                .productPrice(details.getProductPrice())
                .productListPrice(details.getProductListPrice())
                .tax(details.getTax())
                .total(details.getTotal())
                .taxCode(details.getTaxCode())
                .miniStoreId(miniStoreId)
                .miniStoreName(details.getMiniStoreName())
                .shipToId(shipToId)
                .address(details.getAddress())
                .quantity(quantity)
                .build();
    }
}
