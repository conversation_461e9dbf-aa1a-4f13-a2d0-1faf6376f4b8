package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.postgres.entities.BalanceEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface BalanceRepository extends R2dbcRepository<BalanceEntity, Long> {
    @Query("UPDATE dealer.po_balance SET " +
            "account_balance = GREATEST(account_balance - :orderValue, 0), " +
            "available_balance = GREATEST(available_balance - :orderValue, 0) " +
            "WHERE dealer_code = :dealerCode")
    Mono<Long> updatedBalance(String dealerCode, double orderValue);
}
