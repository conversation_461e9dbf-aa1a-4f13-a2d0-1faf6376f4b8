package com.safaricom.dxl.data.postgres.entities;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Table("po_balance")
public class BalanceEntity {
    @Id
    private Long id;
    private String dealerCode;
    private double availableBalance;
    private double accountBalance;
    private LocalDateTime created;
    private LocalDateTime updated;
}
