package com.safaricom.dxl.data.postgres.entities;

import com.safaricom.dxl.data.dto.CartDto;
import com.safaricom.dxl.data.enums.OrderItemStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

import static com.safaricom.dxl.utils.Utilities.localDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Table("po_cart_item")
public class CartEntity {
    @Id
    private Long id;
    private long userId;
    private OrderItemStatus orderItemStatus;
    private String productCode;
    private double price;
    private double tax;
    private double total;
    private int quantity;
    private int miniStoreId;
    private int shipToId;
    private boolean stockAvailable;
    private LocalDateTime created;
    private LocalDateTime updated;

    public static CartEntity of(CartDto dto) {
        return new CartEntity(
                null,
                Long.parseLong(dto.getUserId()),
                OrderItemStatus.PENDING,
                dto.getProductCode(),
                Double.parseDouble(dto.getPrice()),
                Double.parseDouble(dto.getTax()),
                Double.parseDouble(dto.getTotal()),
                Integer.parseInt(dto.getQuantity()),
                Integer.parseInt(dto.getMiniStoreId()),
                Integer.parseInt(dto.getShipToId()),
                dto.isStockAvailable(),
                localDateTime(),
                null
        );
    }

    public static CartEntity of(CartEntity cart, CartDto dto) {
        cart.setPrice(Double.parseDouble(dto.getPrice()));
        cart.setTax(Double.parseDouble(dto.getTax()));
        cart.setTotal(Double.parseDouble(dto.getTotal()));
        cart.setQuantity(Integer.parseInt(dto.getQuantity()));
        cart.setMiniStoreId(Integer.parseInt(dto.getMiniStoreId()));
        cart.setShipToId(Integer.parseInt(dto.getShipToId()));
        cart.setStockAvailable(dto.isStockAvailable());
        cart.setUpdated(localDateTime());
        return cart;
    }
}
