package com.safaricom.dxl.data.postgres.repositories;

import com.safaricom.dxl.data.model.SavedOrderItem;
import com.safaricom.dxl.data.postgres.entities.SavedOrderItemEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface SavedOrderItemRepository extends ReactiveCrudRepository<SavedOrderItemEntity, Long> {
    @Query("SELECT soi.id, soi.product_code, soi.mini_store_id, soi.ship_to_id, soi.quantity" +
            ", p.product_description, p.product_price, p.tax, p.tax_code, p.total" +
            ", m.mini_store_name" +
            ", st.address " +
            "FROM dealer.po_saved_order_item soi " +
            "JOIN dealer.po_products p ON soi.product_code = p.product_code " +
            "JOIN dealer.po_mini_store m ON soi.mini_store_id = m.mini_store_id " +
            "JOIN dealer.po_ship_to st ON soi.ship_to_id = st.ship_to_id " +
            "WHERE soi.order_identifier_id = :orderIdentifierId")
    Flux<SavedOrderItem> findByOrderIdentifierId(String orderIdentifierId);

    Mono<Void> deleteByOrderIdentifierId(String orderIdentifier);

    @Query("SELECT so.id, so.product_code, so.mini_store_id, so.ship_to_id, so.quantity " +
            ", p.product_price, p.tax, p.tax_code, m.mini_store_name, st.address, p.total " +
            "FROM dealer.po_saved_order_item so " +
            "JOIN dealer.po_products p ON so.product_code = p.product_code " +
            "JOIN dealer.po_mini_store m ON so.mini_store_id = m.mini_store_id " +
            "JOIN dealer.po_ship_to st ON so.ship_to_id = st.ship_to_id " +
            "WHERE so.order_identifier_id = :orderIdentifier")
    Flux<SavedOrderItem> customByOrderIdentifierId(String orderIdentifier);
}
