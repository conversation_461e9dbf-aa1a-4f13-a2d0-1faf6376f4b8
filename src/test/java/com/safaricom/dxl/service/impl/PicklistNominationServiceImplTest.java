package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.config.PIIDataDecryption;
import com.safaricom.dxl.data.dto.CollectorInfoDto;
import com.safaricom.dxl.data.dto.PicklistNominationDto;
import com.safaricom.dxl.data.model.*;
import com.safaricom.dxl.data.postgres.entities.PicklistNominationEntity;
import com.safaricom.dxl.data.postgres.repositories.PicklistNominationRepository;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for PicklistNominationServiceImpl
 * Tests all business logic paths with positive, negative, and edge case scenarios
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("PicklistNominationServiceImpl Unit Tests")
class PicklistNominationServiceImplTest {

    @Mock
    private InputValidation validate;

    @Mock
    private Shared shared;

    @Mock
    private SSOToken ssoToken;

    @Mock
    private PicklistNominationRepository picklistNominationRepository;

    @Mock
    private WsResponseMapper responseMapper;

    @Mock
    private PIIDataDecryption piiDataDecryption;

    @InjectMocks
    private PicklistNominationServiceImpl picklistNominationService;

    private Map<String, String> validHeaders;
    private String validDealerCode;
    private PicklistNominationDto validNomineeDto;
    private PicklistNominationEntity existingNomineeEntity;
    private PicklistNominationEntity savedNomineeEntity;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put(X_IDENTITY, "test-user");
        validHeaders.put("Authorization", "Bearer test-token");

        validDealerCode = "DEALER001";
        validNomineeDto = createValidNomineeDto();
        existingNomineeEntity = createExistingNomineeEntity();
        savedNomineeEntity = createSavedNomineeEntity();
        mockSuccessResponse = createMockWsResponse();
        mockErrorResponse = createMockWsResponse();
    }

    // Helper methods for creating test data
    private PicklistNominationDto createValidNomineeDto() {
        PicklistNominationDto dto = new PicklistNominationDto();
        dto.setDocumentNumber("123456789");
        dto.setFirstName("John");
        dto.setMiddleName("Michael");
        dto.setLastName("Doe");
        dto.setMiniStoreName("Main Store");
        dto.setDocumentType("ID_CARD");
        dto.setPhoneNumber("+254712345678");
        dto.setDealerCode("DEALER001");
        return dto;
    }

    private PicklistNominationEntity createExistingNomineeEntity() {
        PicklistNominationEntity entity = new PicklistNominationEntity();
        entity.setId(1L);
        entity.setDocumentNumber("encrypted_doc_number");
        entity.setFirstName("John");
        entity.setMiddleName("Michael");
        entity.setLastName("Doe");
        entity.setMiniStoreName("Main Store");
        entity.setDocumentType("ID_CARD");
        entity.setPhoneNumber("encrypted_phone");
        entity.setDealerCode("DEALER001");
        entity.setCreated(LocalDateTime.now().minusDays(1));
        entity.setCreatedBy("admin");
        entity.setUpdated(LocalDateTime.now());
        entity.setUpdatedBy("admin");
        entity.setStatusFlag("ACTIVE");
        return entity;
    }

    private PicklistNominationEntity createSavedNomineeEntity() {
        PicklistNominationEntity entity = new PicklistNominationEntity();
        entity.setId(2L);
        entity.setDocumentNumber("encrypted_doc_number");
        entity.setFirstName("John");
        entity.setMiddleName("Michael");
        entity.setLastName("Doe");
        entity.setMiniStoreName("Main Store");
        entity.setDocumentType("ID_CARD");
        entity.setPhoneNumber("encrypted_phone");
        entity.setDealerCode("DEALER001");
        entity.setCreated(LocalDateTime.now());
        entity.setCreatedBy("test-user");
        entity.setUpdated(LocalDateTime.now());
        entity.setUpdatedBy("test-user");
        entity.setStatusFlag("ACTIVE");
        return entity;
    }

    private WsResponse createMockWsResponse() {
        return new WsResponse();
    }

    @Nested
    @DisplayName("Add Nominee Tests")
    class AddNomineeTests {

        @Test
        @DisplayName("addNominee_NewNominee_CreatesSuccessfully")
        void addNominee_NewNominee_CreatesSuccessfully() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Add new Nominee"));
                    });

            when(picklistNominationRepository.findByDocumentNumberAndMiniStoreName(anyString(), anyString()))
                    .thenReturn(Mono.empty());

            when(piiDataDecryption.encrypt(validNomineeDto.getDocumentNumber())).thenReturn("encrypted_doc_number");
            when(piiDataDecryption.encrypt(validNomineeDto.getPhoneNumber())).thenReturn("encrypted_phone");
            when(piiDataDecryption.decrypt("encrypted_doc_number")).thenReturn(validNomineeDto.getDocumentNumber());
            when(piiDataDecryption.decrypt("encrypted_phone")).thenReturn(validNomineeDto.getPhoneNumber());

            when(picklistNominationRepository.save(any(PicklistNominationEntity.class)))
                    .thenReturn(Mono.just(savedNomineeEntity));

            when(shared.sendSmsDxl(eq(validHeaders), anyString(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            PicklistNominationDto mockBalance = new PicklistNominationDto();
            when(savedNomineeEntity.toBalance()).thenReturn(mockBalance);
            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(mockBalance), eq(TRANS_ADD_NOMINEE), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findByDocumentNumberAndMiniStoreName(anyString(), anyString());
            verify(picklistNominationRepository).save(any(PicklistNominationEntity.class));
            verify(shared).sendSmsDxl(eq(validHeaders), anyString(), anyString());
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("addNominee_ExistingActiveNominee_ReturnsError")
        void addNominee_ExistingActiveNominee_ReturnsError() {
            // Given
            PicklistNominationEntity activeNominee = createExistingNomineeEntity();
            activeNominee.setStatusFlag("ACTIVE");

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Add new Nominee"));
                    });

            when(picklistNominationRepository.findByDocumentNumberAndMiniStoreName(anyString(), anyString()))
                    .thenReturn(Mono.just(activeNominee));

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), eq("Duplicate ACTIVE record exists"), 
                    eq(ERR_BAD_REQUEST), eq(TRANS_ADD_NOMINEE)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findByDocumentNumberAndMiniStoreName(anyString(), anyString());
            verify(shared).customResponse(eq(validHeaders), any(PartnerInfo.class), eq("Duplicate ACTIVE record exists"), 
                    eq(ERR_BAD_REQUEST), eq(TRANS_ADD_NOMINEE));
            verifyNoInteractions(piiDataDecryption);
        }

        @Test
        @DisplayName("addNominee_ExistingInactiveNominee_CreatesNewNominee")
        void addNominee_ExistingInactiveNominee_CreatesNewNominee() {
            // Given
            PicklistNominationEntity inactiveNominee = createExistingNomineeEntity();
            inactiveNominee.setStatusFlag("RETIRED");

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Add new Nominee"));
                    });

            when(picklistNominationRepository.findByDocumentNumberAndMiniStoreName(anyString(), anyString()))
                    .thenReturn(Mono.just(inactiveNominee));

            when(piiDataDecryption.encrypt(validNomineeDto.getDocumentNumber())).thenReturn("encrypted_doc_number");
            when(piiDataDecryption.encrypt(validNomineeDto.getPhoneNumber())).thenReturn("encrypted_phone");
            when(piiDataDecryption.decrypt("encrypted_doc_number")).thenReturn(validNomineeDto.getDocumentNumber());
            when(piiDataDecryption.decrypt("encrypted_phone")).thenReturn(validNomineeDto.getPhoneNumber());

            when(picklistNominationRepository.save(any(PicklistNominationEntity.class)))
                    .thenReturn(Mono.just(savedNomineeEntity));

            when(shared.sendSmsDxl(eq(validHeaders), anyString(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            PicklistNominationDto mockBalance = new PicklistNominationDto();
            when(savedNomineeEntity.toBalance()).thenReturn(mockBalance);
            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(mockBalance), eq(TRANS_ADD_NOMINEE), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findByDocumentNumberAndMiniStoreName(anyString(), anyString());
            verify(picklistNominationRepository).save(any(PicklistNominationEntity.class));
            verify(shared).sendSmsDxl(eq(validHeaders), anyString(), anyString());
        }

        @Test
        @DisplayName("addNominee_ValidationFailure_ReturnsError")
        void addNominee_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(picklistNominationRepository);
        }

        @Test
        @DisplayName("addNominee_SSOTokenValidationFailure_ExecutesErrorMapping")
        void addNominee_SSOTokenValidationFailure_ExecutesErrorMapping() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<ErrorMapping, Mono<WsResponse>> errorMappingFunction = invocation.getArgument(1);
                        ErrorMapping errorMapping = mock(ErrorMapping.class);
                        return errorMappingFunction.apply(errorMapping);
                    });

            TokenErrorMapping tokenErrorMapping = new TokenErrorMapping("SSO validation failed");
            when(ssoToken.validateSessionAndCode(eq(validHeaders), eq(validDealerCode)))
                    .thenReturn(Mono.just(tokenErrorMapping));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result.cast(Object.class))
                    .expectNextMatches(response -> response instanceof TokenErrorMapping)
                    .verifyComplete();

            verify(ssoToken).validateSessionAndCode(validHeaders, validDealerCode);
        }

        @Test
        @DisplayName("addNominee_RepositoryError_PropagatesError")
        void addNominee_RepositoryError_PropagatesError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Add new Nominee"));
                    });

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(picklistNominationRepository.findByDocumentNumberAndMiniStoreName(anyString(), anyString()))
                    .thenReturn(Mono.error(repositoryException));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(picklistNominationRepository).findByDocumentNumberAndMiniStoreName(anyString(), anyString());
        }

        @Test
        @DisplayName("addNominee_SMSError_HandledGracefully")
        void addNominee_SMSError_HandledGracefully() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Add new Nominee"));
                    });

            when(picklistNominationRepository.findByDocumentNumberAndMiniStoreName(anyString(), anyString()))
                    .thenReturn(Mono.empty());

            when(piiDataDecryption.encrypt(validNomineeDto.getDocumentNumber())).thenReturn("encrypted_doc_number");
            when(piiDataDecryption.encrypt(validNomineeDto.getPhoneNumber())).thenReturn("encrypted_phone");
            when(piiDataDecryption.decrypt("encrypted_doc_number")).thenReturn(validNomineeDto.getDocumentNumber());
            when(piiDataDecryption.decrypt("encrypted_phone")).thenReturn(validNomineeDto.getPhoneNumber());

            when(picklistNominationRepository.save(any(PicklistNominationEntity.class)))
                    .thenReturn(Mono.just(savedNomineeEntity));

            RuntimeException smsException = new RuntimeException("SMS service unavailable");
            when(shared.sendSmsDxl(eq(validHeaders), anyString(), anyString()))
                    .thenReturn(Mono.error(smsException));

            when(shared.customResponse(eq(validHeaders), anyString(), eq("500"), eq(TRANS_SEND_SMS)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).save(any(PicklistNominationEntity.class));
            verify(shared).sendSmsDxl(eq(validHeaders), anyString(), anyString());
            verify(shared).customResponse(eq(validHeaders), anyString(), eq("500"), eq(TRANS_SEND_SMS));
        }
    }

    @Nested
    @DisplayName("Get Active Nominations Tests")
    class GetActiveNominationsTests {

        @Test
        @DisplayName("getActiveNominations_ValidParameters_ReturnsNominations")
        void getActiveNominations_ValidParameters_ReturnsNominations() {
            // Given
            int page = 0;
            int size = 10;
            PageRequest pageRequest = PageRequest.of(page, size);

            List<PicklistNominationEntity> nominations = List.of(
                    createNominationEntity("John", "Doe", "Store A"),
                    createNominationEntity("Jane", "Smith", "Store B")
            );

            List<CollectorInfoDto> expectedDtos = nominations.stream()
                    .map(nomination -> new CollectorInfoDto(
                            nomination.getId(),
                            nomination.getFirstName() + " " + nomination.getLastName(),
                            nomination.getMiniStoreName(),
                            nomination.getCreatedBy(),
                            nomination.getCreated()
                    )).toList();

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Fetch Active Nominations"));
                    });

            when(picklistNominationRepository.findActiveNominationsByDealerCode(validDealerCode, pageRequest))
                    .thenReturn(Flux.fromIterable(nominations));

            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(expectedDtos), eq(TRANS_FETCH_NOMINEE), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.getActiveNominations(validHeaders, validDealerCode, page, size);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findActiveNominationsByDealerCode(validDealerCode, pageRequest);
            verify(responseMapper).setApiResponse(ERR_SUCCESS, expectedDtos, TRANS_FETCH_NOMINEE, FALSE, validHeaders);
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("getActiveNominations_EmptyResults_ReturnsNotFoundError")
        void getActiveNominations_EmptyResults_ReturnsNotFoundError() {
            // Given
            int page = 0;
            int size = 10;
            PageRequest pageRequest = PageRequest.of(page, size);

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Fetch Active Nominations"));
                    });

            when(picklistNominationRepository.findActiveNominationsByDealerCode(validDealerCode, pageRequest))
                    .thenReturn(Flux.empty());

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), eq("No active nominations found."),
                    eq(ERR_NOT_FOUND), eq(TRANS_FETCH_NOMINEE)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.getActiveNominations(validHeaders, validDealerCode, page, size);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findActiveNominationsByDealerCode(validDealerCode, pageRequest);
            verify(shared).customResponse(eq(validHeaders), any(PartnerInfo.class), eq("No active nominations found."),
                    eq(ERR_NOT_FOUND), eq(TRANS_FETCH_NOMINEE));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("getActiveNominations_NominationWithMiddleName_FormatsNameCorrectly")
        void getActiveNominations_NominationWithMiddleName_FormatsNameCorrectly() {
            // Given
            int page = 0;
            int size = 10;
            PicklistNominationEntity nomination = createNominationEntity("John", "Doe", "Store A");
            nomination.setMiddleName("Michael");

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Fetch Active Nominations"));
                    });

            when(picklistNominationRepository.findActiveNominationsByDealerCode(validDealerCode, PageRequest.of(page, size)))
                    .thenReturn(Flux.just(nomination));

            // Expected DTO with formatted name including middle name
            CollectorInfoDto expectedDto = new CollectorInfoDto(
                    nomination.getId(),
                    "John Michael Doe", // Full name with middle name
                    nomination.getMiniStoreName(),
                    nomination.getCreatedBy(),
                    nomination.getCreated()
            );

            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(List.of(expectedDto)), eq(TRANS_FETCH_NOMINEE), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.getActiveNominations(validHeaders, validDealerCode, page, size);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(responseMapper).setApiResponse(ERR_SUCCESS, List.of(expectedDto), TRANS_FETCH_NOMINEE, FALSE, validHeaders);
        }

        @ParameterizedTest
        @ValueSource(ints = {0, 1, 5, 10, 50})
        @DisplayName("getActiveNominations_VariousPageSizes_HandledCorrectly")
        void getActiveNominations_VariousPageSizes_HandledCorrectly(int size) {
            // Given
            int page = 0;
            PageRequest pageRequest = PageRequest.of(page, size);

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Fetch Active Nominations"));
                    });

            when(picklistNominationRepository.findActiveNominationsByDealerCode(validDealerCode, pageRequest))
                    .thenReturn(Flux.empty());

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), eq("No active nominations found."),
                    eq(ERR_NOT_FOUND), eq(TRANS_FETCH_NOMINEE)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.getActiveNominations(validHeaders, validDealerCode, page, size);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findActiveNominationsByDealerCode(validDealerCode, pageRequest);
        }

        private PicklistNominationEntity createNominationEntity(String firstName, String lastName, String storeName) {
            PicklistNominationEntity entity = new PicklistNominationEntity();
            entity.setId(1L);
            entity.setFirstName(firstName);
            entity.setLastName(lastName);
            entity.setMiniStoreName(storeName);
            entity.setCreatedBy("admin");
            entity.setCreated(LocalDateTime.now().minusDays(1));
            return entity;
        }
    }

    @Nested
    @DisplayName("Retire Nomination Tests")
    class RetireNominationTests {

        @Test
        @DisplayName("retireNomination_ValidActiveNomination_RetiresSuccessfully")
        void retireNomination_ValidActiveNomination_RetiresSuccessfully() {
            // Given
            Long nominationId = 1L;
            PicklistNominationEntity activeNomination = createExistingNomineeEntity();
            activeNomination.setStatusFlag("ACTIVE");

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Retire Nominee"));
                    });

            when(picklistNominationRepository.findById(nominationId))
                    .thenReturn(Mono.just(activeNomination));

            PicklistNominationEntity retiredNomination = createExistingNomineeEntity();
            retiredNomination.setStatusFlag("RETIRED");
            retiredNomination.setUpdatedBy(validHeaders.get(X_IDENTITY));
            when(picklistNominationRepository.save(any(PicklistNominationEntity.class)))
                    .thenReturn(Mono.just(retiredNomination));

            PicklistNominationDto mockBalance = new PicklistNominationDto();
            when(retiredNomination.toBalance()).thenReturn(mockBalance);
            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), eq(mockBalance), eq(TRANS_RETIRE_NOMINEE), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.retireNomination(validHeaders, nominationId, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findById(nominationId);
            verify(picklistNominationRepository).save(any(PicklistNominationEntity.class));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("retireNomination_AlreadyRetiredNomination_ReturnsError")
        void retireNomination_AlreadyRetiredNomination_ReturnsError() {
            // Given
            Long nominationId = 1L;
            PicklistNominationEntity retiredNomination = createExistingNomineeEntity();
            retiredNomination.setStatusFlag("RETIRED");

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Retire Nominee"));
                    });

            when(picklistNominationRepository.findById(nominationId))
                    .thenReturn(Mono.just(retiredNomination));

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), eq("Nominee has been Retired"), 
                    eq(ERR_BAD_REQUEST), eq(TRANS_RETIRE_NOMINEE)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.retireNomination(validHeaders, nominationId, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findById(nominationId);
            verify(picklistNominationRepository, never()).save(any(PicklistNominationEntity.class));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("retireNomination_NonExistentNomination_ReturnsNotFoundError")
        void retireNomination_NonExistentNomination_ReturnsNotFoundError() {
            // Given
            Long nominationId = 999L;

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Retire Nominee"));
                    });

            when(picklistNominationRepository.findById(nominationId))
                    .thenReturn(Mono.empty());

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), eq("Nomination ID does not exist"), 
                    eq(ERR_NOT_FOUND), eq(TRANS_RETIRE_NOMINEE)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.retireNomination(validHeaders, nominationId, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(picklistNominationRepository).findById(nominationId);
            verify(shared).customResponse(eq(validHeaders), any(PartnerInfo.class), eq("Nomination ID does not exist"), 
                    eq(ERR_NOT_FOUND), eq(TRANS_RETIRE_NOMINEE));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("retireNomination_NullNominationId_ThrowsException")
        void retireNomination_NullNominationId_ThrowsException() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Retire Nominee"));
                    });

            // When & Then
            StepVerifier.create(picklistNominationService.retireNomination(validHeaders, null, validDealerCode))
                    .expectError(NullPointerException.class)
                    .verify();
        }

        @Test
        @DisplayName("retireNomination_RepositoryError_PropagatesError")
        void retireNomination_RepositoryError_PropagatesError() {
            // Given
            Long nominationId = 1L;

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Retire Nominee"));
                    });

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(picklistNominationRepository.findById(nominationId))
                    .thenReturn(Mono.error(repositoryException));

            // When
            Mono<WsResponse> result = picklistNominationService.retireNomination(validHeaders, nominationId, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(picklistNominationRepository).findById(nominationId);
        }
    }

    @Nested
    @DisplayName("Integration and Edge Cases Tests")
    class IntegrationAndEdgeCasesTests {

        @Test
        @DisplayName("allMethods_NullHeaders_HandledBySharedValidation")
        void allMethods_NullHeaders_HandledBySharedValidation() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When & Then
            StepVerifier.create(picklistNominationService.addNominee(null, validNomineeDto, validDealerCode))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(picklistNominationService.getActiveNominations(null, validDealerCode, 0, 10))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(picklistNominationService.retireNomination(null, 1L, validDealerCode))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared, times(3)).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
        }

        @Test
        @DisplayName("messageGeneration_ValidNominee_CreatesCorrectMessage")
        void messageGeneration_ValidNominee_CreatesCorrectMessage() {
            // This test verifies the private generateMessage method indirectly
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Add new Nominee"));
                    });

            when(picklistNominationRepository.findByDocumentNumberAndMiniStoreName(anyString(), anyString()))
                    .thenReturn(Mono.empty());

            when(piiDataDecryption.encrypt(anyString())).thenReturn("encrypted_value");
            when(piiDataDecryption.decrypt("encrypted_value")).thenReturn("decrypted_value");

            PicklistNominationEntity savedEntity = createSavedNomineeEntity();
            savedEntity.setFirstName("John");
            savedEntity.setLastName("Doe");
            savedEntity.setMiniStoreName("Test Store");
            when(picklistNominationRepository.save(any(PicklistNominationEntity.class)))
                    .thenReturn(Mono.just(savedEntity));

            // Capture the SMS message
            when(shared.sendSmsDxl(eq(validHeaders), anyString(), argThat(message -> 
                    message.contains("John") && message.contains("Doe") && message.contains("Test Store"))))
                    .thenReturn(Mono.just(mockSuccessResponse));

            PicklistNominationDto mockBalance = new PicklistNominationDto();
            when(savedEntity.toBalance()).thenReturn(mockBalance);
            when(responseMapper.setApiResponse(any(), any(), any(), any(), any()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationService.addNominee(validHeaders, validNomineeDto, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(shared).sendSmsDxl(eq(validHeaders), anyString(), argThat(message -> 
                    message.contains("authorized to collect stock from")));
        }
    }
}
