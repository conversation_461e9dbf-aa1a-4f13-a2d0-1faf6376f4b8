package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.s3.S3Properties;
import com.safaricom.dxl.data.dto.CartItemsDto;
import com.safaricom.dxl.data.model.*;
import com.safaricom.dxl.data.response.OrderItemResponse;
import com.safaricom.dxl.data.xml.CreateOrderResponse;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import java.net.URL;
import com.safaricom.dxl.data.postgres.entities.CartEntity;
import com.safaricom.dxl.data.postgres.entities.OrderEntity;
import com.safaricom.dxl.data.postgres.entities.OrderItemsEntity;
import com.safaricom.dxl.data.postgres.entities.StockEntity;
import com.safaricom.dxl.data.postgres.repositories.*;
import com.safaricom.dxl.utils.*;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.reactive.TransactionalOperator;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

import static com.safaricom.dxl.data.enums.OrderItemStatus.*;
import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for OrderServiceImpl
 * Tests all business logic paths with positive, negative, and edge case scenarios
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OrderServiceImpl Unit Tests")
class OrderServiceImplTest {

    @Mock private WsResponseMapper responseMapper;
    @Mock private InputValidation validate;
    @Mock private Shared shared;
    @Mock private SSOToken ssoToken;
    @Mock private CartRepository cartRepository;
    @Mock private OrderRepository orderRepository;
    @Mock private ExcelService excelService;
    @Mock private S3Properties s3Properties;
    @Mock private MsConfigProperties msConfigProperties;
    @Mock private StockRepository stockRepository;
    @Mock private MyWebClient client;
    @Mock private ERPOrder erpOrder;
    @Mock private OrderItemsRepositoryPort orderItemsRepositoryPort;
    @Mock private OrderItemsRepository orderItemsRepository;
    @Mock private TransactionalOperator transactionalOperator;

    @InjectMocks
    private OrderServiceImpl orderService;

    private Map<String, String> validHeaders;
    private String validDealerCode;
    private CartItemsDto validCartItemsDto;
    private List<CartEntity> cartEntities;
    private List<OrderItemsEntity> orderItemsEntities;
    private OrderEntity orderEntity;
    private List<OrderDetail> orderDetails;
    private Order populatedOrder;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;
    private ERPResponse successErpResponse;
    private ERPResponse failureErpResponse;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put("X-Source-System", "test-system");
        validHeaders.put("Authorization", "Bearer test-token");

        validDealerCode = "DEALER001";
        validCartItemsDto = createValidCartItemsDto();
        cartEntities = createCartEntities();
        orderItemsEntities = createOrderItemsEntities();
        orderEntity = createOrderEntity();
        orderDetails = createOrderDetails();
        populatedOrder = createPopulatedOrder();
        mockSuccessResponse = new WsResponse();
        mockErrorResponse = new WsResponse();
        successErpResponse = createSuccessErpResponse();
        failureErpResponse = createFailureErpResponse();
    }

    // Helper methods for creating test data
    private CartItemsDto createValidCartItemsDto() {
        CartItemsDto dto = new CartItemsDto();
        dto.setCartIds(Arrays.asList(1L, 2L, 3L));
        dto.setUserId(12345L);
        return dto;
    }

    private List<CartEntity> createCartEntities() {
        CartEntity cart1 = new CartEntity();
        cart1.setId(1L);
        cart1.setUserId(12345L);
        cart1.setProductCode("PROD001");
        cart1.setQuantity(5);
        cart1.setMiniStoreId(1);
        cart1.setPrice(100.0);

        CartEntity cart2 = new CartEntity();
        cart2.setId(2L);
        cart2.setUserId(12345L);
        cart2.setProductCode("PROD002");
        cart2.setQuantity(3);
        cart2.setMiniStoreId(2);
        cart2.setPrice(50.0);

        return Arrays.asList(cart1, cart2);
    }

    private List<OrderItemsEntity> createOrderItemsEntities() {
        OrderItemsEntity item1 = new OrderItemsEntity();
        item1.setId(1L);
        item1.setUserId(12345L);
        item1.setProductCode("PROD001");
        item1.setQuantity(5);
        item1.setMiniStoreId(1);
        item1.setOrderItemStatus(PENDING);

        OrderItemsEntity item2 = new OrderItemsEntity();
        item2.setId(2L);
        item2.setUserId(12345L);
        item2.setProductCode("PROD002");
        item2.setQuantity(3);
        item2.setMiniStoreId(2);
        item2.setOrderItemStatus(PENDING);

        return Arrays.asList(item1, item2);
    }

    private OrderEntity createOrderEntity() {
        OrderEntity order = new OrderEntity();
        order.setId("order-123");
        order.setDealerCode("DEALER001");
        order.setUserId(12345L);
        order.setErpOrderId("ERP-ORDER-123");
        order.setLineItemId(System.currentTimeMillis()); // RequestId is actually lineItemId in the model
        return order;
    }

    private List<OrderDetail> createOrderDetails() {
        OrderDetail detail1 = new OrderDetail();
        detail1.setId("order-1");
        detail1.setDealerCode("DEALER001");
        detail1.setUserId(12345L);
        detail1.setFirstName("John");
        detail1.setEmail("<EMAIL>");
        detail1.setOrderItemIds(Set.of(1L, 2L));

        OrderDetail detail2 = new OrderDetail();
        detail2.setId("order-2");
        detail2.setDealerCode("DEALER001");
        detail2.setUserId(12345L);
        detail2.setFirstName("Jane");
        detail2.setEmail("<EMAIL>");
        detail2.setOrderItemIds(Set.of(3L, 4L));

        return Arrays.asList(detail1, detail2);
    }

    private Order createPopulatedOrder() {
        Order order = Order.builder()
            .dealerCode("DEALER001")
            .orderList(Arrays.asList(mock(OrderItemResponse.class), mock(OrderItemResponse.class)))
            .build();
        return order;
    }

    private ERPResponse createSuccessErpResponse() {
        return new ERPResponse("S", "Success", "ERP-ORDER-123");
    }

    private ERPResponse createFailureErpResponse() {
        return new ERPResponse("F", "ERP Processing Failed", null);
    }

    private StockEntity createStockEntity(String productCode, int miniStoreId, int quantity) {
        StockEntity stock = new StockEntity();
        stock.setProductCode(productCode);
        stock.setMiniStoreId(miniStoreId);
        stock.setQuantity(quantity);
        return stock;
    }

    @Nested
    @DisplayName("Create Order Tests")
    class CreateOrderTests {

        @Test
        @DisplayName("createOrder_ValidRequest_CreatesOrderSuccessfully")
        void createOrder_ValidRequest_CreatesOrderSuccessfully() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Create Order"));
                    });

            when(cartRepository.findAllById(validCartItemsDto.getCartIds()))
                    .thenReturn(Flux.fromIterable(cartEntities));

            when(orderItemsRepository.findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(anyLong(), anyString(), anyInt(), eq(PENDING)))
                    .thenReturn(Mono.empty());

            when(orderItemsRepository.save(any(OrderItemsEntity.class)))
                    .thenReturn(Mono.just(orderItemsEntities.get(0)))
                    .thenReturn(Mono.just(orderItemsEntities.get(1)));

            // Stock validation - sufficient stock available
            when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(1, "PROD001", 5))
                    .thenReturn(Mono.just(createStockEntity("PROD001", 1, 10)));
            when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(2, "PROD002", 3))
                    .thenReturn(Mono.just(createStockEntity("PROD002", 2, 8)));

            // Idempotency check - no existing order
            when(orderRepository.findById(anyString())).thenReturn(Mono.empty());

            // ERP success
            CreateOrderResponse.Response mockResponse = mock(CreateOrderResponse.Response.class);
            when(mockResponse.getResponseCode()).thenReturn("S");
            when(mockResponse.getResponseMessage()).thenReturn("Success");
            when(mockResponse.getOrderNumber()).thenReturn("ERP-ORDER-123");
            when(erpOrder.create(eq(validHeaders), eq(validDealerCode), any(), eq(client), anyString(), anyString()))
                    .thenReturn(Mono.just(mockResponse));

            // Transactional operations
            when(transactionalOperator.transactional(any(Mono.class))).thenAnswer(invocation -> invocation.getArgument(0));

            when(stockRepository.findByMiniStoreIdAndProductCode(1, "PROD001"))
                    .thenReturn(Mono.just(createStockEntity("PROD001", 1, 10)));
            when(stockRepository.findByMiniStoreIdAndProductCode(2, "PROD002"))
                    .thenReturn(Mono.just(createStockEntity("PROD002", 2, 8)));

            when(stockRepository.save(any(StockEntity.class)))
                    .thenReturn(Mono.just(new StockEntity()));

            when(orderItemsRepositoryPort.updateOrderId(any(), anyString())).thenReturn(Mono.empty());
            when(orderItemsRepositoryPort.updateOrderItemsStatus(any(), eq(BOOKED))).thenReturn(Mono.empty());
            when(cartRepository.deleteAllById(validCartItemsDto.getCartIds())).thenReturn(Mono.empty());
            when(orderRepository.save(any(OrderEntity.class))).thenReturn(Mono.just(orderEntity));

            when(msConfigProperties.getOrderMessage()).thenReturn("Your order %s has been created successfully");
            when(shared.sendSmsDxl(eq(validHeaders), anyString(), anyString())).thenReturn(Mono.just(mockSuccessResponse));
            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(), eq(TRANS_ADD_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).findAllById(validCartItemsDto.getCartIds());
            verify(erpOrder).create(eq(validHeaders), eq(validDealerCode), any(), eq(client), anyString(), anyString());
            verify(orderRepository).save(any(OrderEntity.class));
            verify(shared).sendSmsDxl(eq(validHeaders), anyString(), anyString());
        }

        @Test
        @DisplayName("createOrder_EmptyOrderItems_ReturnsError")
        void createOrder_EmptyOrderItems_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Create Order"));
                    });

            when(cartRepository.findAllById(validCartItemsDto.getCartIds()))
                    .thenReturn(Flux.empty());

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("No orders CREATED as they were created before."), eq(ERR_BAD_REQUEST), eq(TRANS_ADD_ORDER)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(cartRepository).findAllById(validCartItemsDto.getCartIds());
            verify(shared).customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("No orders CREATED as they were created before."), eq(ERR_BAD_REQUEST), eq(TRANS_ADD_ORDER));
        }

        @Test
        @DisplayName("createOrder_InsufficientStock_ReturnsStockError")
        void createOrder_InsufficientStock_ReturnsStockError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Create Order"));
                    });

            when(cartRepository.findAllById(validCartItemsDto.getCartIds()))
                    .thenReturn(Flux.fromIterable(cartEntities));

            when(orderItemsRepository.findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(anyLong(), anyString(), anyInt(), eq(PENDING)))
                    .thenReturn(Mono.empty());

            when(orderItemsRepository.save(any(OrderItemsEntity.class)))
                    .thenReturn(Mono.just(orderItemsEntities.get(0)))
                    .thenReturn(Mono.just(orderItemsEntities.get(1)));

            // Insufficient stock for first product
            when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(1, "PROD001", 5))
                    .thenReturn(Mono.empty());
            when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(2, "PROD002", 3))
                    .thenReturn(Mono.just(createStockEntity("PROD002", 2, 8)));

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("Stock insufficient for PROD001."), eq(ERR_BAD_REQUEST), eq(TRANS_ADD_ORDER)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("Stock insufficient for PROD001."), eq(ERR_BAD_REQUEST), eq(TRANS_ADD_ORDER));
        }

        @Test
        @DisplayName("createOrder_ExistingOrder_ReturnsIdempotencyError")
        void createOrder_ExistingOrder_ReturnsIdempotencyError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Create Order"));
                    });

            when(cartRepository.findAllById(validCartItemsDto.getCartIds()))
                    .thenReturn(Flux.fromIterable(cartEntities));

            when(orderItemsRepository.findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(anyLong(), anyString(), anyInt(), eq(PENDING)))
                    .thenReturn(Mono.empty());

            when(orderItemsRepository.save(any(OrderItemsEntity.class)))
                    .thenReturn(Mono.just(orderItemsEntities.get(0)))
                    .thenReturn(Mono.just(orderItemsEntities.get(1)));

            // Stock validation - sufficient stock available
            when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(anyInt(), anyString(), anyInt()))
                    .thenReturn(Mono.just(new StockEntity()));

            // Existing order found
            when(orderRepository.findById(anyString())).thenReturn(Mono.just(orderEntity));
            when(orderItemsRepository.deleteAllById(any())).thenReturn(Mono.empty());

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("You have already created another similar Order in the same mini-store. Change order details to proceed."), 
                    eq(ERR_BAD_REQUEST), eq(TRANS_ADD_ORDER)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(orderRepository).findById(anyString());
            verify(orderItemsRepository).deleteAllById(any());
        }

        @Test
        @DisplayName("createOrder_ERPFailure_MarksItemsAsFailedAndReturnsError")
        void createOrder_ERPFailure_MarksItemsAsFailedAndReturnsError() {
            // Given
            setupBasicCreateOrderMocks();

            // ERP failure
            CreateOrderResponse.Response mockFailureResponse = mock(CreateOrderResponse.Response.class);
            when(mockFailureResponse.getResponseCode()).thenReturn("F");
            when(mockFailureResponse.getResponseMessage()).thenReturn("ERP Processing Failed");
            when(mockFailureResponse.getOrderNumber()).thenReturn(null);
            
            // Override the lenient mock with failure response
            lenient().when(erpOrder.create(any(), any(), any(), any(), any(), any()))
                    .thenReturn(Mono.just(mockFailureResponse));

            when(transactionalOperator.transactional(any(Mono.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(orderItemsRepositoryPort.updateOrderItemsStatus(any(), eq(FAILED))).thenReturn(Mono.empty());

            lenient().when(shared.customResponse(any(), any(PartnerInfo.class), 
                    eq("ERP Processing Failed"), eq(ERR_BAD_REQUEST), eq(TRANS_ADD_ORDER)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(erpOrder).create(any(), any(), any(), any(), any(), any());
            verify(orderItemsRepositoryPort).updateOrderItemsStatus(any(), eq(FAILED));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq("500"), eq("ERP Processing Failed"));
        }

        @Test
        @DisplayName("createOrder_ValidationFailure_ReturnsError")
        void createOrder_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(cartRepository);
        }

        @Test
        @DisplayName("createOrder_SSOTokenValidationFailure_ExecutesErrorMapping")
        void createOrder_SSOTokenValidationFailure_ExecutesErrorMapping() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<ErrorMapping, Mono<WsResponse>> errorMappingFunction = invocation.getArgument(1);
                        ErrorMapping errorMapping = mock(ErrorMapping.class);
                        return errorMappingFunction.apply(errorMapping);
                    });

            TokenErrorMapping tokenErrorMapping = new TokenErrorMapping("SSO validation failed");
            when(ssoToken.validateSessionAndCode(eq(validHeaders), eq(validDealerCode)))
                    .thenReturn(Mono.just(tokenErrorMapping));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result.cast(Object.class))
                    .expectNextMatches(response -> response instanceof TokenErrorMapping)
                    .verifyComplete();

            verify(ssoToken).validateSessionAndCode(validHeaders, validDealerCode);
        }

        @Test
        @DisplayName("createOrder_ERPException_MarksItemsAsOrderErrorAndReturnsError")
        void createOrder_ERPException_MarksItemsAsOrderErrorAndReturnsError() {
            // Given
            setupBasicCreateOrderMocks();

            // ERP throws exception - use lenient to avoid strict stubbing issues
            RuntimeException erpException = new RuntimeException("ERP service unavailable");
            lenient().when(erpOrder.create(any(), any(), any(), any(), any(), any()))
                    .thenReturn(Mono.error(erpException));

            when(transactionalOperator.transactional(any(Mono.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(orderItemsRepositoryPort.updateOrderItemsStatus(any(), eq(ORDER_ERROR))).thenReturn(Mono.empty());

            lenient().when(shared.customResponse(any(), any(String.class), any(), any()))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(orderItemsRepositoryPort).updateOrderItemsStatus(any(), eq(ORDER_ERROR));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq("500"), eq("Request failed, please try again later"));
        }

        @Test
        @DisplayName("createOrder_ExistingPendingOrderItem_MarksAsFailedAndCreatesNew")
        void createOrder_ExistingPendingOrderItem_MarksAsFailedAndCreatesNew() {
            // Given
            setupBasicCreateOrderMocks();

            OrderItemsEntity existingPendingItem = new OrderItemsEntity();
            existingPendingItem.setId(10L);
            existingPendingItem.setOrderItemStatus(PENDING);

            when(orderItemsRepository.findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(anyLong(), anyString(), anyInt(), eq(PENDING)))
                    .thenReturn(Mono.just(existingPendingItem))
                    .thenReturn(Mono.empty());

            when(orderItemsRepository.save(existingPendingItem))
                    .thenReturn(Mono.just(existingPendingItem));

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            // Verify existing item was marked as FAILED
            verify(orderItemsRepository).save(argThat(item -> 
                item.getOrderItemStatus() == FAILED));
        }

        private void setupBasicCreateOrderMocks() {
            lenient().when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Create Order"));
                    });

            lenient().when(cartRepository.findAllById(validCartItemsDto.getCartIds()))
                    .thenReturn(Flux.fromIterable(cartEntities));

            lenient().when(orderItemsRepository.findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(anyLong(), anyString(), anyInt(), eq(PENDING)))
                    .thenReturn(Mono.empty());

            lenient().when(orderItemsRepository.save(any(OrderItemsEntity.class)))
                    .thenReturn(Mono.just(orderItemsEntities.get(0)))
                    .thenReturn(Mono.just(orderItemsEntities.get(1)));

            lenient().when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(anyInt(), anyString(), anyInt()))
                    .thenReturn(Mono.just(new StockEntity()));

            lenient().when(orderRepository.findById(anyString())).thenReturn(Mono.empty());

            CreateOrderResponse.Response mockErpResponse = mock(CreateOrderResponse.Response.class);
            lenient().when(mockErpResponse.getResponseCode()).thenReturn("S");
            lenient().when(mockErpResponse.getResponseMessage()).thenReturn("Success");
            lenient().when(mockErpResponse.getOrderNumber()).thenReturn("ERP-ORDER-123");
            
            // Use lenient to avoid strict stubbing issues with dynamic arguments
            lenient().when(erpOrder.create(any(), any(), any(), any(), any(), any()))
                    .thenReturn(Mono.just(mockErpResponse));

            lenient().when(transactionalOperator.transactional(any(Mono.class))).thenAnswer(invocation -> invocation.getArgument(0));
            lenient().when(stockRepository.findByMiniStoreIdAndProductCode(anyInt(), anyString()))
                    .thenReturn(Mono.just(createStockEntity("PROD001", 1, 10)));
            lenient().when(stockRepository.save(any(StockEntity.class))).thenReturn(Mono.just(new StockEntity()));
            lenient().when(orderItemsRepositoryPort.updateOrderId(any(), anyString())).thenReturn(Mono.empty());
            lenient().when(orderItemsRepositoryPort.updateOrderItemsStatus(any(), eq(BOOKED))).thenReturn(Mono.empty());
            lenient().when(cartRepository.deleteAllById(any())).thenReturn(Mono.empty());
            lenient().when(orderRepository.save(any(OrderEntity.class))).thenReturn(Mono.just(orderEntity));

            lenient().when(msConfigProperties.getOrderMessage()).thenReturn("Your order %s has been created successfully");
            lenient().when(msConfigProperties.getCreateOrderSystemRef()).thenReturn("MSPARTNER");
            lenient().when(shared.sendSmsDxl(any(), anyString(), anyString())).thenReturn(Mono.just(mockSuccessResponse));
            lenient().when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(), eq(TRANS_ADD_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));
            
            // Mock shared.streamToKafka calls
            lenient().doNothing().when(shared).streamToKafka(any(), any(PartnerInfo.class), any(), any());
            
            // Mock shared.customResponse calls for error handling
            lenient().when(shared.customResponse(any(), any(PartnerInfo.class), anyString(), anyString(), anyString()))
                    .thenReturn(Mono.just(mockErrorResponse));
            lenient().when(shared.customResponse(any(), anyString(), anyString(), anyString()))
                    .thenReturn(Mono.just(mockErrorResponse));
        }
    }

    @Nested
    @DisplayName("Fetch Order By Dealer Code Tests")
    class FetchOrderByDealerCodeTests {

        @Test
        @DisplayName("fetchOrderByDealerCode_ValidParameters_ReturnsOrdersWithPagination")
        void fetchOrderByDealerCode_ValidParameters_ReturnsOrdersWithPagination() {
            // Given
            int pageNo = 0;
            int pageSize = 10;
            String fromDate = "2023-01-01";
            String toDate = "2023-01-31";
            Long totalCount = 25L;

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "fetchOrderByDealerCode"));
                    });

            when(orderRepository.customByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class), eq(pageNo), eq(pageSize)))
                    .thenReturn(Flux.fromIterable(orderDetails));

            when(orderItemsRepository.customFindByOrderItemIds(any()))
                    .thenReturn(Flux.just(mock(OrderItemResponse.class), mock(OrderItemResponse.class)));

            when(orderRepository.customCountByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class)))
                    .thenReturn(Mono.just(totalCount));

            PaginationList expectedPagination = new PaginationList(totalCount, Arrays.asList(populatedOrder, populatedOrder));
            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(PaginationList.class), eq(TRANS_FETCH_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.fetchOrderByDealerCode(validHeaders, validDealerCode, pageNo, pageSize, fromDate, toDate);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderRepository).customByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class), eq(pageNo), eq(pageSize));
            verify(orderRepository).customCountByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class));
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("fetchOrderByDealerCode_ValidationFailure_ReturnsError")
        void fetchOrderByDealerCode_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.fetchOrderByDealerCode(validHeaders, validDealerCode, 0, 10, "2023-01-01", "2023-01-31");

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(orderRepository);
        }

        @ParameterizedTest
        @ValueSource(ints = {0, 1, 5, 10, 50})
        @DisplayName("fetchOrderByDealerCode_VariousPageSizes_HandledCorrectly")
        void fetchOrderByDealerCode_VariousPageSizes_HandledCorrectly(int pageSize) {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "fetchOrderByDealerCode"));
                    });

            when(orderRepository.customByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class), eq(0), eq(pageSize)))
                    .thenReturn(Flux.empty());

            when(orderRepository.customCountByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class)))
                    .thenReturn(Mono.just(0L));

            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(PaginationList.class), eq(TRANS_FETCH_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.fetchOrderByDealerCode(validHeaders, validDealerCode, 0, pageSize, "2023-01-01", "2023-01-31");

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("Fetch Order By Order Number Tests")
    class FetchOrderByOrderNumberTests {

        @Test
        @DisplayName("fetchOrderByOrderNumber_ValidOrderNumber_ReturnsOrder")
        void fetchOrderByOrderNumber_ValidOrderNumber_ReturnsOrder() {
            // Given
            String orderNumber = "ERP-ORDER-123";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "fetchOrderByOrderNumber"));
                    });

            when(orderRepository.customByErpOrderIdAndDealerCode(orderNumber, validDealerCode))
                    .thenReturn(Mono.just(orderDetails.get(0)));

            when(orderItemsRepository.customFindByOrderItemIds(any()))
                    .thenReturn(Flux.just(mock(OrderItemResponse.class), mock(OrderItemResponse.class)));

            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(Order.class), eq(TRANS_FETCH_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.fetchOrderByOrderNumber(validHeaders, validDealerCode, orderNumber);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderRepository).customByErpOrderIdAndDealerCode(orderNumber, validDealerCode);
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("fetchOrderByOrderNumber_OrderNotFound_ReturnsNotFoundError")
        void fetchOrderByOrderNumber_OrderNotFound_ReturnsNotFoundError() {
            // Given
            String orderNumber = "NON-EXISTENT-ORDER";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "fetchOrderByOrderNumber"));
                    });

            when(orderRepository.customByErpOrderIdAndDealerCode(orderNumber, validDealerCode))
                    .thenReturn(Mono.empty());

            when(shared.customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("Not Found"), eq(ERR_NOT_FOUND), eq(TRANS_FETCH_ORDER)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.fetchOrderByOrderNumber(validHeaders, validDealerCode, orderNumber);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(orderRepository).customByErpOrderIdAndDealerCode(orderNumber, validDealerCode);
            verify(shared).customResponse(eq(validHeaders), any(PartnerInfo.class), 
                    eq("Not Found"), eq(ERR_NOT_FOUND), eq(TRANS_FETCH_ORDER));
        }
    }

    @Nested
    @DisplayName("Export to S3 Tests")
    class ExportToS3Tests {

        @Test
        @DisplayName("exportToS3_ValidParameters_ExportsSuccessfully")
        void exportToS3_ValidParameters_ExportsSuccessfully() {
            // Given
            String startDate = "2023-01-01";
            String endDate = "2023-01-31";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "export to s3"));
                    });

            when(excelService.exportExcelToS3(validHeaders, validDealerCode, startDate, endDate))
                    .thenReturn(Mono.empty());

            when(shared.processFileRequest(validHeaders, validDealerCode, startDate, endDate, "Exemption"))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.exportToS3(validHeaders, validDealerCode, startDate, endDate);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(excelService).exportExcelToS3(validHeaders, validDealerCode, startDate, endDate);
            verify(shared).processFileRequest(validHeaders, validDealerCode, startDate, endDate, "Exemption");
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("exportToS3_ValidationFailure_ReturnsError")
        void exportToS3_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.exportToS3(validHeaders, validDealerCode, "2023-01-01", "2023-01-31");

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(excelService);
        }
    }

    @Nested
    @DisplayName("Fetch Excel URL Tests")
    class FetchExcelUrlTests {

        @Test
        @DisplayName("fetchExcelUrl_ValidRequest_ReturnsPreSignedUrl")
        void fetchExcelUrl_ValidRequest_ReturnsPreSignedUrl() {
            // Given
            String fileName = "orders_export_dealer001.xlsx";
            String preSignedUrl = "https://s3.amazonaws.com/bucket/orders_export_dealer001.xlsx?signed=true";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "fetch Excel"));
                    });

            when(s3Properties.getOrdersPrefix()).thenReturn("orders/");
            when(excelService.getFileNameAndPath(validDealerCode, "orders/"))
                    .thenReturn(Mono.just(fileName));

            when(shared.preSignedUrl(fileName)).thenReturn(mock(URL.class));
            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(), eq(TRANS_FETCH_EXCEL_FILE_FROM_S3), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.fetchExcelUrl(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(excelService).getFileNameAndPath(validDealerCode, "orders/");
            verify(shared).preSignedUrl(fileName);
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("fetchExcelUrl_ValidationFailure_ReturnsError")
        void fetchExcelUrl_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderService.fetchExcelUrl(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(excelService);
        }
    }

    @Nested
    @DisplayName("Integration and Edge Cases Tests")
    class IntegrationAndEdgeCasesTests {

        @Test
        @DisplayName("allMethods_NullHeaders_HandledBySharedValidation")
        void allMethods_NullHeaders_HandledBySharedValidation() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When & Then - Test null headers are handled gracefully
            StepVerifier.create(orderService.createOrder(Map.of(), validDealerCode, validCartItemsDto))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(orderService.fetchOrderByDealerCode(Map.of(), validDealerCode, 0, 10, "2023-01-01", "2023-01-31"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(orderService.fetchOrderByOrderNumber(Map.of(), validDealerCode, "ORDER-123"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(orderService.exportToS3(Map.of(), validDealerCode, "2023-01-01", "2023-01-31"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(orderService.fetchExcelUrl(Map.of(), validDealerCode))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared, times(5)).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
        }

        @Test
        @DisplayName("generateMessage_ValidParameters_FormatsCorrectly")
        void generateMessage_ValidParameters_FormatsCorrectly() {
            // This test verifies the private generateMessage method indirectly
            // We can't test it directly, but it's covered through the create order flow
            
            // Given
            setupBasicCreateOrderMocksForIntegration();
            when(msConfigProperties.getOrderMessage()).thenReturn("Dear customer, your order %s has been processed successfully.");

            // When
            Mono<WsResponse> result = orderService.createOrder(validHeaders, validDealerCode, validCartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            // Verify SMS was sent with formatted message
            verify(shared).sendSmsDxl(eq(validHeaders), anyString(), 
                    argThat(message -> message.contains("Dear customer") && message.contains("ERP-ORDER-123")));
        }

        @Test
        @DisplayName("populateOrderWithItems_ValidOrderDetail_PopulatesOrderList")
        void populateOrderWithItems_ValidOrderDetail_PopulatesOrderList() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "fetchOrderByDealerCode"));
                    });

            when(orderRepository.customByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class), eq(0), eq(10)))
                    .thenReturn(Flux.fromIterable(orderDetails));

            List<OrderItemResponse> orderItemResponses = Arrays.asList(mock(OrderItemResponse.class), mock(OrderItemResponse.class));
            when(orderItemsRepository.customFindByOrderItemIds(any()))
                    .thenReturn(Flux.fromIterable(orderItemResponses));

            when(orderRepository.customCountByDealerCode(eq(validDealerCode), any(LocalDateTime.class), any(LocalDateTime.class)))
                    .thenReturn(Mono.just(2L));

            when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(PaginationList.class), eq(TRANS_FETCH_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderService.fetchOrderByDealerCode(validHeaders, validDealerCode, 0, 10, "2023-01-01", "2023-01-31");

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            // Verify that order items were populated for each order
            verify(orderItemsRepository, times(orderDetails.size())).customFindByOrderItemIds(any());
        }
        
        private void setupBasicCreateOrderMocksForIntegration() {
            lenient().when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Create Order"));
                    });

            lenient().when(cartRepository.findAllById(validCartItemsDto.getCartIds()))
                    .thenReturn(Flux.fromIterable(cartEntities));

            lenient().when(orderItemsRepository.findByUserIdAndProductCodeAndMiniStoreIdAndOrderItemStatus(anyLong(), anyString(), anyInt(), eq(PENDING)))
                    .thenReturn(Mono.empty());

            lenient().when(orderItemsRepository.save(any(OrderItemsEntity.class)))
                    .thenReturn(Mono.just(orderItemsEntities.get(0)))
                    .thenReturn(Mono.just(orderItemsEntities.get(1)));

            lenient().when(stockRepository.findByMiniStoreIdAndProductCodeAndQuantityGreaterThanEqual(anyInt(), anyString(), anyInt()))
                    .thenReturn(Mono.just(new StockEntity()));

            lenient().when(orderRepository.findById(anyString())).thenReturn(Mono.empty());

            CreateOrderResponse.Response mockErpResponse = mock(CreateOrderResponse.Response.class);
            lenient().when(mockErpResponse.getResponseCode()).thenReturn("S");
            lenient().when(mockErpResponse.getResponseMessage()).thenReturn("Success");
            lenient().when(mockErpResponse.getOrderNumber()).thenReturn("ERP-ORDER-123");
            
            // Use lenient to avoid strict stubbing issues with dynamic arguments
            lenient().when(erpOrder.create(any(), any(), any(), any(), any(), any()))
                    .thenReturn(Mono.just(mockErpResponse));

            lenient().when(transactionalOperator.transactional(any(Mono.class))).thenAnswer(invocation -> invocation.getArgument(0));
            lenient().when(stockRepository.findByMiniStoreIdAndProductCode(anyInt(), anyString()))
                    .thenReturn(Mono.just(createStockEntity("PROD001", 1, 10)));
            lenient().when(stockRepository.save(any(StockEntity.class))).thenReturn(Mono.just(new StockEntity()));
            lenient().when(orderItemsRepositoryPort.updateOrderId(any(), anyString())).thenReturn(Mono.empty());
            lenient().when(orderItemsRepositoryPort.updateOrderItemsStatus(any(), eq(BOOKED))).thenReturn(Mono.empty());
            lenient().when(cartRepository.deleteAllById(any())).thenReturn(Mono.empty());
            lenient().when(orderRepository.save(any(OrderEntity.class))).thenReturn(Mono.just(orderEntity));

            lenient().when(msConfigProperties.getOrderMessage()).thenReturn("Your order %s has been created successfully");
            lenient().when(msConfigProperties.getCreateOrderSystemRef()).thenReturn("MSPARTNER");
            lenient().when(shared.sendSmsDxl(any(), anyString(), anyString())).thenReturn(Mono.just(mockSuccessResponse));
            lenient().when(responseMapper.setApiResponse(eq(ERR_SUCCESS), any(), eq(TRANS_ADD_ORDER), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));
            
            // Mock shared.streamToKafka calls
            lenient().doNothing().when(shared).streamToKafka(any(), any(PartnerInfo.class), any(), any());
        }

    }
}
