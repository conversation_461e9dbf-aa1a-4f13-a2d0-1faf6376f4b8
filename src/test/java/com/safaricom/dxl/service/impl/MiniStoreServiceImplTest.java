package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.model.ErrorMapping;
import com.safaricom.dxl.data.model.PartnerInfo;
import com.safaricom.dxl.data.model.TokenErrorMapping;
import com.safaricom.dxl.data.model.ValidationContext;
import com.safaricom.dxl.data.postgres.entities.MiniStoreEntity;
import com.safaricom.dxl.data.postgres.repositories.MiniStoreRepository;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.NULL;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for MiniStoreServiceImpl
 * Tests all business logic paths with positive, negative, and edge case scenarios
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MiniStoreServiceImpl Unit Tests")
class MiniStoreServiceImplTest {

    @Mock
    private WsResponseMapper responseMapper;

    @Mock
    private SSOToken ssoToken;

    @Mock
    private Shared shared;

    @Mock
    private MiniStoreRepository miniStoreRepository;

    @Mock
    private InputValidation validate;

    @InjectMocks
    private MiniStoreServiceImpl miniStoreService;

    private Map<String, String> validHeaders;
    private String validDealerCode;
    private List<MiniStoreEntity> miniStoreEntities;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put("X-Source-System", "test-system");
        validHeaders.put("Authorization", "Bearer test-token");

        validDealerCode = "DEALER001";
        miniStoreEntities = createMiniStoreEntities();
        mockSuccessResponse = createMockWsResponse();
        mockErrorResponse = createMockWsResponse();
    }

    // Helper methods for creating test data
    private List<MiniStoreEntity> createMiniStoreEntities() {
        MiniStoreEntity store1 = new MiniStoreEntity();
        store1.setId(1L);
        store1.setMiniStoreId(1);
        store1.setMiniStoreCode("STORE001");
        store1.setMiniStoreName("Store A");

        MiniStoreEntity store2 = new MiniStoreEntity();
        store2.setId(3L);
        store2.setMiniStoreId(3); // Not 252 (filtered out)
        store2.setMiniStoreCode("STORE003");
        store2.setMiniStoreName("Store B");

        return List.of(store1, store2);
    }

    private List<MiniStoreEntity> createMiniStoreEntitiesWithFiltered() {
        MiniStoreEntity store1 = new MiniStoreEntity();
        store1.setId(1L);
        store1.setMiniStoreId(1);
        store1.setMiniStoreCode("STORE001");
        store1.setMiniStoreName("Store A");

        MiniStoreEntity filteredStore = new MiniStoreEntity();
        filteredStore.setId(252L);
        filteredStore.setMiniStoreId(252); // This should be filtered out
        filteredStore.setMiniStoreCode("STORE252");
        filteredStore.setMiniStoreName("Filtered Store");

        MiniStoreEntity store3 = new MiniStoreEntity();
        store3.setId(3L);
        store3.setMiniStoreId(3);
        store3.setMiniStoreCode("STORE003");
        store3.setMiniStoreName("Store C");

        return List.of(store1, filteredStore, store3);
    }

    private WsResponse createMockWsResponse() {
        return new WsResponse();
    }

    @Nested
    @DisplayName("Fetch All Mini Stores Tests")
    class FetchAllMiniStoresTests {

        @Test
        @DisplayName("fetchAllMiniStores_ValidParameters_ReturnsFilteredMiniStores")
        void fetchAllMiniStores_ValidParameters_ReturnsFilteredMiniStores() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(miniStoreEntities));
            when(responseMapper.setApiResponse(ERR_SUCCESS, miniStoreEntities, TRANS_FETCH_MINISTORES, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(miniStoreRepository).findAll();
            verify(responseMapper).setApiResponse(ERR_SUCCESS, miniStoreEntities, TRANS_FETCH_MINISTORES, FALSE, validHeaders);
            verify(shared).streamToKafka(eq(validHeaders), any(PartnerInfo.class), eq(null), eq(null));
        }

        @Test
        @DisplayName("fetchAllMiniStores_WithStoreId252_FiltersOutStore252")
        void fetchAllMiniStores_WithStoreId252_FiltersOutStore252() {
            // Given
            List<MiniStoreEntity> storesWithFiltered = createMiniStoreEntitiesWithFiltered();
            List<MiniStoreEntity> expectedFilteredStores = List.of(
                    storesWithFiltered.get(0), // Store 1
                    storesWithFiltered.get(2)  // Store 3 (Store 252 is filtered out)
            );

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(storesWithFiltered));
            when(responseMapper.setApiResponse(ERR_SUCCESS, expectedFilteredStores, TRANS_FETCH_MINISTORES, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(miniStoreRepository).findAll();
            verify(responseMapper).setApiResponse(ERR_SUCCESS, expectedFilteredStores, TRANS_FETCH_MINISTORES, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchAllMiniStores_EmptyStoresList_ReturnsNullResponse")
        void fetchAllMiniStores_EmptyStoresList_ReturnsNullResponse() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.empty());
            when(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_FETCH_MINISTORES, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(miniStoreRepository).findAll();
            verify(responseMapper).setApiResponse(ERR_SUCCESS, NULL, TRANS_FETCH_MINISTORES, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchAllMiniStores_OnlyStore252_ReturnsEmptyListAsNull")
        void fetchAllMiniStores_OnlyStore252_ReturnsEmptyListAsNull() {
            // Given
            MiniStoreEntity filteredStore = new MiniStoreEntity();
            filteredStore.setId(252L);
            filteredStore.setMiniStoreId(252);
            filteredStore.setMiniStoreCode("STORE252");
            filteredStore.setMiniStoreName("Filtered Store");
            List<MiniStoreEntity> onlyFilteredStore = List.of(filteredStore);

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(onlyFilteredStore));
            when(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_FETCH_MINISTORES, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(miniStoreRepository).findAll();
            verify(responseMapper).setApiResponse(ERR_SUCCESS, NULL, TRANS_FETCH_MINISTORES, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchAllMiniStores_ValidationFailure_ReturnsError")
        void fetchAllMiniStores_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(miniStoreRepository);
        }

        @Test
        @DisplayName("fetchAllMiniStores_RepositoryError_PropagatesError")
        void fetchAllMiniStores_RepositoryError_PropagatesError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(miniStoreRepository.findAll()).thenReturn(Flux.error(repositoryException));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(miniStoreRepository).findAll();
        }

        @Test
        @DisplayName("fetchAllMiniStores_SSOTokenValidationFailure_ExecutesErrorMapping")
        void fetchAllMiniStores_SSOTokenValidationFailure_ExecutesErrorMapping() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        // Input validation passes, but SSO validation should fail
                        Function<ErrorMapping, Mono<WsResponse>> errorMappingFunction = invocation.getArgument(1);
                        ErrorMapping errorMapping = mock(ErrorMapping.class);
                        return errorMappingFunction.apply(errorMapping);
                    });

            // Mock SSO token validation to return error response
            TokenErrorMapping tokenErrorMapping = new TokenErrorMapping("SSO validation failed");
            when(ssoToken.validateSessionAndCode(validHeaders, validDealerCode))
                    .thenReturn(Mono.just(tokenErrorMapping));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then - The error mapping function returns the TokenErrorMapping directly
            StepVerifier.create(result.cast(Object.class))
                    .expectNextMatches(TokenErrorMapping.class::isInstance)
                    .verifyComplete();

            verify(ssoToken).validateSessionAndCode(validHeaders, validDealerCode);
        }

        @ParameterizedTest
        @ValueSource(strings = {"", "   ", "DEALER-999", "dealer123"})
        @DisplayName("fetchAllMiniStores_VariousDealerCodes_ProcessedCorrectly")
        void fetchAllMiniStores_VariousDealerCodes_ProcessedCorrectly(String dealerCode) {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(miniStoreEntities));
            when(responseMapper.setApiResponse(ERR_SUCCESS, miniStoreEntities, TRANS_FETCH_MINISTORES, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(miniStoreRepository).findAll();
            verify(responseMapper).setApiResponse(ERR_SUCCESS, miniStoreEntities, TRANS_FETCH_MINISTORES, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchAllMiniStores_NullHeaders_HandledBySharedValidation")
        void fetchAllMiniStores_NullHeaders_HandledBySharedValidation() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(null, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(miniStoreRepository);
        }

        @Test
        @DisplayName("fetchAllMiniStores_NullDealerCode_HandledBySharedValidation")
        void fetchAllMiniStores_NullDealerCode_HandledBySharedValidation() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, null);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(miniStoreRepository);
        }

        @Test
        @DisplayName("fetchAllMiniStores_EmptyHeaders_HandledBySharedValidation")
        void fetchAllMiniStores_EmptyHeaders_HandledBySharedValidation() {
            // Given
            Map<String, String> emptyHeaders = new HashMap<>();
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(emptyHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(miniStoreRepository);
        }
    }

    @Nested
    @DisplayName("Integration and Edge Cases Tests")
    class IntegrationAndEdgeCasesTests {

        @Test
        @DisplayName("fetchAllMiniStores_ValidationContextCreated_WithCorrectParameters")
        void fetchAllMiniStores_ValidationContextCreated_WithCorrectParameters() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        // Verify ValidationContext is created correctly
                        ValidationContext context = invocation.getArgument(3);
                        assert context != null;
                        assert context.headers().equals(validHeaders);
                        assert context.partnerInfo().getDealerCode().equals(validDealerCode);
                        assert TRANS_FETCH_MINISTORES.equals(context.params());
                        assert ERR_BAD_REQUEST.equals(context.code());
                        assert "getAllMiniStores".equals(context.process());

                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(miniStoreEntities));
            when(responseMapper.setApiResponse(any(), any(), any(), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchAllMiniStores_StoreFiltering_WorksWithMixedIds")
        void fetchAllMiniStores_StoreFiltering_WorksWithMixedIds() {
            // Given
            MiniStoreEntity store1 = new MiniStoreEntity();
            store1.setId(1L);
            store1.setMiniStoreId(1);
            store1.setMiniStoreCode("STORE001");
            store1.setMiniStoreName("Store 1");
            
            MiniStoreEntity store252 = new MiniStoreEntity();
            store252.setId(252L);
            store252.setMiniStoreId(252);
            store252.setMiniStoreCode("STORE252");
            store252.setMiniStoreName("Store 252");
            
            MiniStoreEntity store253 = new MiniStoreEntity();
            store253.setId(253L);
            store253.setMiniStoreId(253);
            store253.setMiniStoreCode("STORE253");
            store253.setMiniStoreName("Store 253");
            
            MiniStoreEntity store0 = new MiniStoreEntity();
            store0.setId(0L);
            store0.setMiniStoreId(0);
            store0.setMiniStoreCode("STORE000");
            store0.setMiniStoreName("Store 0");

            List<MiniStoreEntity> mixedStores = List.of(store1, store252, store253, store0);
            List<MiniStoreEntity> expectedFiltered = List.of(store1, store253, store0); // All except 252

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(mixedStores));
            when(responseMapper.setApiResponse(ERR_SUCCESS, expectedFiltered, TRANS_FETCH_MINISTORES, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(responseMapper).setApiResponse(ERR_SUCCESS, expectedFiltered, TRANS_FETCH_MINISTORES, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchAllMiniStores_MultipleCallsConcurrently_HandledCorrectly")
        void fetchAllMiniStores_MultipleCallsConcurrently_HandledCorrectly() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validDealerCode, "Get All MiniStores"));
                    });

            when(miniStoreRepository.findAll()).thenReturn(Flux.fromIterable(miniStoreEntities));
            when(responseMapper.setApiResponse(any(), any(), any(), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When - Execute concurrent operations
            Mono<WsResponse> result1 = miniStoreService.fetchAllMiniStores(validHeaders, validDealerCode);
            Mono<WsResponse> result2 = miniStoreService.fetchAllMiniStores(validHeaders, "DEALER002");
            Mono<WsResponse> result3 = miniStoreService.fetchAllMiniStores(validHeaders, "DEALER003");

            // Then
            StepVerifier.create(Mono.zip(result1, result2, result3))
                    .expectNextMatches(tuple ->
                            tuple.getT1().equals(mockSuccessResponse) &&
                                    tuple.getT2().equals(mockSuccessResponse) &&
                                    tuple.getT3().equals(mockSuccessResponse))
                    .verifyComplete();
        }
    }
}
