package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.model.DataStreaming;
import com.safaricom.dxl.webflux.starter.config.WsKafkaProperties;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import com.safaricom.dxl.webflux.starter.service.WsStarterStreamProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for KafkaStreamingService
 * Tests all business logic paths with positive, negative, and edge case scenarios
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("KafkaStreamingService Unit Tests")
class KafkaStreamingServiceTest {

    @Mock
    private WsStarterStreamProducer starterStreamProducer;

    @Mock
    private WsKafkaProperties wsKafkaProperties;

    @Mock
    private WsStarterService wsStarterService;

    @InjectMocks
    private KafkaStreamingService kafkaStreamingService;

    private DataStreaming validDataStreaming;
    private String testTopic;
    private long startTime;

    @BeforeEach
    void setUp() {
        // Given
        validDataStreaming = createValidDataStreaming();
        testTopic = "test-topic";
        startTime = System.currentTimeMillis() - 1000L; // 1 second ago

        when(wsKafkaProperties.getTopic()).thenReturn(testTopic);
    }

    // Helper methods for creating test data
    private DataStreaming createValidDataStreaming() {
        DataStreaming dataStreaming = new DataStreaming();
        dataStreaming.setTransactionId("test-request-123");
        dataStreaming.setDealerCode("DEALER001");
        dataStreaming.setUsername("testuser");
        dataStreaming.setResource("TEST_RESOURCE");
        dataStreaming.setServiceName("TEST_SERVICE");
        return dataStreaming;
    }

    @Nested
    @DisplayName("Stream to Kafka Tests")
    class StreamToKafkaTests {

        @Test
        @DisplayName("streamToKafka_ValidDataStreaming_SetsResponseTimeAndSerializesAndProduces")
        void streamToKafka_ValidDataStreaming_SetsResponseTimeAndSerializesAndProduces() {
            // Given
            String serializedJson = "{\"requestId\":\"test-request-123\",\"dealerCode\":\"DEALER001\"}";
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn(serializedJson);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);

            // Then
            verify(wsKafkaProperties).getTopic();
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, serializedJson);
            
            // Verify response time was set (should be > 0)
            assert validDataStreaming.getResponseTime() > 0;
        }

        @Test
        @DisplayName("streamToKafka_NullDataStreaming_ProducesEmptyStringWithoutSerialization")
        void streamToKafka_NullDataStreaming_ProducesEmptyStringWithoutSerialization() {
            // Given - null dataStreaming
            
            // When
            kafkaStreamingService.streamToKafka(null, startTime);

            // Then
            verify(wsKafkaProperties).getTopic();
            verify(starterStreamProducer).produce(testTopic, "");
            verifyNoInteractions(wsStarterService);
        }

        @Test
        @DisplayName("streamToKafka_ValidDataStreaming_CalculatesCorrectResponseTime")
        void streamToKafka_ValidDataStreaming_CalculatesCorrectResponseTime() {
            // Given
            long fixedStartTime = 1000L;
            long currentTime = System.currentTimeMillis();
            String serializedJson = "{\"test\":\"data\"}";
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn(serializedJson);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, fixedStartTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, serializedJson);
            
            // Verify response time is calculated correctly (should be approximately current time - start time)
            long expectedResponseTime = currentTime - fixedStartTime;
            assert validDataStreaming.getResponseTime() >= expectedResponseTime - 100; // Allow 100ms tolerance
            assert validDataStreaming.getResponseTime() <= expectedResponseTime + 100;
        }

        @ParameterizedTest
        @ValueSource(longs = {0L, -1000L, Long.MIN_VALUE})
        @DisplayName("streamToKafka_EdgeCaseStartTimes_HandlesCorrectly")
        void streamToKafka_EdgeCaseStartTimes_HandlesCorrectly(long edgeStartTime) {
            // Given
            String serializedJson = "{\"edge\":\"case\"}";
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn(serializedJson);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, edgeStartTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, serializedJson);
            
            // Verify response time was set (for edge cases, can be negative for extreme start times)
            // The important thing is that it's not 0, showing the calculation occurred
            assert validDataStreaming.getResponseTime() != 0;
        }

        @Test
        @DisplayName("streamToKafka_SerializationReturnsNull_ProducesNullString")
        void streamToKafka_SerializationReturnsNull_ProducesNullString() {
            // Given
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn(null);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, null);
            
            // Verify response time was still set
            assert validDataStreaming.getResponseTime() > 0;
        }

        @Test
        @DisplayName("streamToKafka_SerializationReturnsEmptyString_ProducesEmptyString")
        void streamToKafka_SerializationReturnsEmptyString_ProducesEmptyString() {
            // Given
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn("");

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, "");
            
            // Verify response time was set
            assert validDataStreaming.getResponseTime() > 0;
        }

        @Test
        @DisplayName("streamToKafka_LargeJsonPayload_HandlesCorrectly")
        void streamToKafka_LargeJsonPayload_HandlesCorrectly() {
            // Given
            String largeJson = "{\"data\":\"" + "x".repeat(10000) +
                    "\"}";
            
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn(largeJson);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, largeJson);
            
            // Verify response time was set
            assert validDataStreaming.getResponseTime() > 0;
        }

        @Test
        @DisplayName("streamToKafka_MultipleCallsWithSameInstance_UpdatesResponseTimeEachTime")
        void streamToKafka_MultipleCallsWithSameInstance_UpdatesResponseTimeEachTime() {
            // Given
            String serializedJson1 = "{\"call\":\"first\"}";
            String serializedJson2 = "{\"call\":\"second\"}";
            
            when(wsStarterService.serializeToJson(any(DataStreaming.class)))
                    .thenReturn(serializedJson1)
                    .thenReturn(serializedJson2);

            // When - First call
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);
            long firstResponseTime = validDataStreaming.getResponseTime();

            // When - Second call with different start time (simulate 10ms later)
            long secondStartTime = startTime + 10;
            kafkaStreamingService.streamToKafka(validDataStreaming, secondStartTime);
            long secondResponseTime = validDataStreaming.getResponseTime();

            // Then
            verify(wsStarterService, times(2)).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, serializedJson1);
            verify(starterStreamProducer).produce(testTopic, serializedJson2);
            
            // Verify response times are different
            assert firstResponseTime != secondResponseTime;
            assert firstResponseTime > 0;
            assert secondResponseTime > 0;
        }

        @Test
        @DisplayName("streamToKafka_ModifiedDataStreamingObject_SerializesCurrentState")
        void streamToKafka_ModifiedDataStreamingObject_SerializesCurrentState() {
            // Given
            String initialJson = "{\"initial\":\"state\"}";
            when(wsStarterService.serializeToJson(any(DataStreaming.class))).thenReturn(initialJson);

            // Modify the data streaming object before calling
            validDataStreaming.setTransactionId("modified-request-id");
            validDataStreaming.setResource("MODIFIED_RESOURCE");

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, initialJson);
            
            // Verify the modified object was passed to serialization
            assert "modified-request-id".equals(validDataStreaming.getTransactionId());
            assert "MODIFIED_RESOURCE".equals(validDataStreaming.getResource());
            assert validDataStreaming.getResponseTime() > 0;
        }
    }

    @Nested
    @DisplayName("Integration and Edge Cases Tests")
    class IntegrationAndEdgeCasesTests {

        @Test
        @DisplayName("streamToKafka_AllMockedDependenciesInteractCorrectly")
        void streamToKafka_AllMockedDependenciesInteractCorrectly() {
            // Given
            String testJson = "{\"integration\":\"test\"}";
            when(wsStarterService.serializeToJson(validDataStreaming)).thenReturn(testJson);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, startTime);

            // Then - Verify correct interaction sequence
            verify(wsKafkaProperties, times(1)).getTopic();
            verify(wsStarterService, times(1)).serializeToJson(validDataStreaming);
            verify(starterStreamProducer, times(1)).produce(testTopic, testJson);
            
            // Verify no other interactions
            verifyNoMoreInteractions(wsKafkaProperties, wsStarterService, starterStreamProducer);
        }

        @Test
        @DisplayName("streamToKafka_ConcurrentCalls_HandledIndependently")
        void streamToKafka_ConcurrentCalls_HandledIndependently() {
            // Given
            DataStreaming dataStreaming1 = createValidDataStreaming();
            dataStreaming1.setTransactionId("concurrent-request-1");
            
            DataStreaming dataStreaming2 = createValidDataStreaming();
            dataStreaming2.setTransactionId("concurrent-request-2");
            
            String json1 = "{\"request\":\"1\"}";
            String json2 = "{\"request\":\"2\"}";
            
            when(wsStarterService.serializeToJson(dataStreaming1)).thenReturn(json1);
            when(wsStarterService.serializeToJson(dataStreaming2)).thenReturn(json2);

            // When - Simulate concurrent calls
            kafkaStreamingService.streamToKafka(dataStreaming1, startTime);
            kafkaStreamingService.streamToKafka(dataStreaming2, startTime - 100);

            // Then
            verify(wsStarterService).serializeToJson(dataStreaming1);
            verify(wsStarterService).serializeToJson(dataStreaming2);
            verify(starterStreamProducer).produce(testTopic, json1);
            verify(starterStreamProducer).produce(testTopic, json2);
            
            // Both objects should have response times set
            assert dataStreaming1.getResponseTime() > 0;
            assert dataStreaming2.getResponseTime() > 0;
        }

        @Test
        @DisplayName("streamToKafka_ExtremeStartTime_HandlesProperly")
        void streamToKafka_ExtremeStartTime_HandlesProperly() {
            // Given
            long extremeStartTime = Long.MAX_VALUE; // This will result in negative response time
            String testJson = "{\"extreme\":\"test\"}";
            when(wsStarterService.serializeToJson(validDataStreaming)).thenReturn(testJson);

            // When
            kafkaStreamingService.streamToKafka(validDataStreaming, extremeStartTime);

            // Then
            verify(wsStarterService).serializeToJson(validDataStreaming);
            verify(starterStreamProducer).produce(testTopic, testJson);
            
            // Response time will be negative for extreme future start time, but method should still work
            // This tests the robustness of the calculation
            assert validDataStreaming.getResponseTime() != 0; // Will be negative, but set
        }
    }
}
