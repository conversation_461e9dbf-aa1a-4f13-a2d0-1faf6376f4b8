package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.CartDto;
import com.safaricom.dxl.data.enums.OrderItemStatus;
import com.safaricom.dxl.data.model.*;
import com.safaricom.dxl.data.postgres.entities.CartEntity;
import com.safaricom.dxl.data.postgres.repositories.CartRepository;
import com.safaricom.dxl.data.response.CartResponse;
import com.safaricom.dxl.utils.InputValidation;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for CartServiceImpl
 * Tests all business logic paths with positive, negative, and edge case scenarios
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CartServiceImpl Unit Tests")
class CartServiceImplTest {

    @Mock
    private WsResponseMapper responseMapper;

    @Mock
    private InputValidation validate;

    @Mock
    private Shared shared;

    @Mock
    private SSOToken ssoToken;

    @Mock
    private CartRepository cartRepository;

    @InjectMocks
    private CartServiceImpl cartService;

    private Map<String, String> validHeaders;
    private CartDto validCartDto;
    private CartEntity existingCartEntity;
    private CartEntity newCartEntity;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;
    private List<CartResponse> cartResponseList;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put("X-Source-System", "test-system");
        validHeaders.put("Authorization", "Bearer test-token");

        validCartDto = createValidCartDto();
        existingCartEntity = createExistingCartEntity();
        newCartEntity = createNewCartEntity();
        mockSuccessResponse = createMockWsResponse();
        mockErrorResponse = createMockWsResponse();
        cartResponseList = createCartResponseList();
    }

    // Helper methods for creating test data
    private CartDto createValidCartDto() {
        CartDto dto = new CartDto();
        dto.setDealerCode("DEALER001");
        dto.setUserId("12345");
        dto.setProductCode("PROD001");
        dto.setPrice("100.00");
        dto.setTax("10.00");
        dto.setTotal("110.00");
        dto.setQuantity("5");
        dto.setMiniStoreId("1");
        dto.setShipToId("2");
        dto.setStockAvailable(true);
        return dto;
    }

    private CartEntity createExistingCartEntity() {
        CartEntity entity = new CartEntity();
        entity.setId(1L);
        entity.setUserId(12345L);
        entity.setOrderItemStatus(OrderItemStatus.PENDING);
        entity.setProductCode("PROD001");
        entity.setPrice(90.00);
        entity.setTax(9.00);
        entity.setTotal(99.00);
        entity.setQuantity(3);
        entity.setMiniStoreId(1);
        entity.setShipToId(2);
        entity.setStockAvailable(true);
        entity.setCreated(LocalDateTime.now().minusHours(1));
        entity.setUpdated(null);
        return entity;
    }

    private CartEntity createNewCartEntity() {
        CartEntity entity = new CartEntity();
        entity.setId(2L);
        entity.setUserId(12345L);
        entity.setOrderItemStatus(OrderItemStatus.PENDING);
        entity.setProductCode("PROD001");
        entity.setPrice(100.00);
        entity.setTax(10.00);
        entity.setTotal(110.00);
        entity.setQuantity(5);
        entity.setMiniStoreId(1);
        entity.setShipToId(2);
        entity.setStockAvailable(true);
        entity.setCreated(LocalDateTime.now());
        entity.setUpdated(null);
        return entity;
    }

    private CartEntity createUpdatedCartEntity() {
        CartEntity entity = createExistingCartEntity();
        entity.setPrice(100.00);
        entity.setTax(10.00);
        entity.setTotal(110.00);
        entity.setQuantity(5);
        entity.setUpdated(LocalDateTime.now());
        return entity;
    }

    private List<CartResponse> createCartResponseList() {
        CartEntity cartEntity1 = createExistingCartEntity();
        CartResponse response1 = new CartResponse(cartEntity1, "Product 1 Description", 1L,
                1, "Electronics", "Store A");

        CartEntity cartEntity2 = createNewCartEntity();
        CartResponse response2 = new CartResponse(cartEntity2, "Product 2 Description", 2L,
                2, "Accessories", "Store B");

        return List.of(response1, response2);
    }

    private WsResponse createMockWsResponse() {
        // Just create a basic mock - actual fields will be set by mocked responseMapper
        return new WsResponse();
    }

    @Nested
    @DisplayName("Add Cart Item Tests")
    class AddCartItemTests {

        @Test
        @DisplayName("add_ValidCartDto_ExistingItem_UpdatesSuccessfully")
        void add_ValidCartDto_ExistingItem_UpdatesSuccessfully() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validCartDto.getDealerCode(), "Update Cart"));
                    });

            when(cartRepository.findByUserIdAndProductCode(12345L, validCartDto.getProductCode()))
                    .thenReturn(Mono.just(existingCartEntity));

            CartEntity updatedEntity = createUpdatedCartEntity();
            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.just(updatedEntity));

            when(responseMapper.setApiResponse(ERR_SUCCESS, updatedEntity, TRANS_ADD_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).findByUserIdAndProductCode(12345L, validCartDto.getProductCode());
            verify(cartRepository).save(any(CartEntity.class));
            verify(responseMapper).setApiResponse(ERR_SUCCESS, updatedEntity, TRANS_ADD_CART, FALSE, validHeaders);
        }

        @Test
        @DisplayName("add_ValidCartDto_NewItem_CreatesSuccessfully")
        void add_ValidCartDto_NewItem_CreatesSuccessfully() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validCartDto.getDealerCode(), "Save Cart"));
                    });

            when(cartRepository.findByUserIdAndProductCode(12345L, validCartDto.getProductCode()))
                    .thenReturn(Mono.empty());

            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.just(newCartEntity));

            when(responseMapper.setApiResponse(ERR_SUCCESS, newCartEntity, TRANS_ADD_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).findByUserIdAndProductCode(12345L, validCartDto.getProductCode());
            verify(cartRepository).save(any(CartEntity.class));
            verify(responseMapper).setApiResponse(ERR_SUCCESS, newCartEntity, TRANS_ADD_CART, FALSE, validHeaders);
        }

        @Test
        @DisplayName("add_ValidationFailure_ReturnsError")
        void add_ValidationFailure_ReturnsError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(cartRepository);
        }

        @Test
        @DisplayName("add_RepositoryError_UpdateExistingItem_PropagatesError")
        void add_RepositoryError_UpdateExistingItem_PropagatesError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validCartDto.getDealerCode(), "Update Cart"));
                    });

            when(cartRepository.findByUserIdAndProductCode(12345L, validCartDto.getProductCode()))
                    .thenReturn(Mono.just(existingCartEntity));

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.error(repositoryException));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(cartRepository).findByUserIdAndProductCode(12345L, validCartDto.getProductCode());
            verify(cartRepository).save(any(CartEntity.class));
        }

        @Test
        @DisplayName("add_RepositoryError_NewItem_PropagatesError")
        void add_RepositoryError_NewItem_PropagatesError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validCartDto.getDealerCode(), "Save Cart"));
                    });

            when(cartRepository.findByUserIdAndProductCode(12345L, validCartDto.getProductCode()))
                    .thenReturn(Mono.empty());

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.error(repositoryException));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(cartRepository).findByUserIdAndProductCode(12345L, validCartDto.getProductCode());
            verify(cartRepository).save(any(CartEntity.class));
        }

        @Test
        @DisplayName("add_FindRepositoryError_PropagatesError")
        void add_FindRepositoryError_PropagatesError() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validCartDto.getDealerCode(), "Update Cart"));
                    });

            RuntimeException findException = new RuntimeException("Find operation failed");
            when(cartRepository.findByUserIdAndProductCode(12345L, validCartDto.getProductCode()))
                    .thenReturn(Mono.error(findException));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(cartRepository).findByUserIdAndProductCode(12345L, validCartDto.getProductCode());
            verify(cartRepository, never()).save(any(CartEntity.class));
        }

        @ParameterizedTest
        @ValueSource(strings = {"0", "-1"})
        @DisplayName("add_EdgeCaseUserIds_ProcessesCorrectly")
        void add_EdgeCaseUserIds_ProcessesCorrectly(String edgeUserId) {
            // Given
            CartDto edgeDto = createValidCartDto();
            edgeDto.setUserId(edgeUserId);

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), edgeDto.getDealerCode(), "Add Cart"));
                    });

            when(cartRepository.findByUserIdAndProductCode(anyLong(), eq(edgeDto.getProductCode())))
                    .thenReturn(Mono.empty());
            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.just(newCartEntity));
            when(responseMapper.setApiResponse(ERR_SUCCESS, newCartEntity, TRANS_ADD_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, edgeDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("add_InvalidUserIdFormat_HandledByValidation")
        void add_InvalidUserIdFormat_HandledByValidation() {
            // Given
            CartDto invalidDto = createValidCartDto();
            invalidDto.setUserId("invalid");

            // Mock validation failure for invalid userId format
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, invalidDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("add_SSOTokenValidationFailure_ExecutesErrorMapping")
        void add_SSOTokenValidationFailure_ExecutesErrorMapping() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        // Input validation passes, but SSO validation should fail
                        Function<ErrorMapping, Mono<WsResponse>> errorMappingFunction = invocation.getArgument(1);
                        // Create a mock ErrorMapping to trigger the error mapping lambda
                        ErrorMapping errorMapping = mock(ErrorMapping.class);
                        return errorMappingFunction.apply(errorMapping);
                    });

            // Mock SSO token validation to return error response
            TokenErrorMapping tokenErrorMapping = new TokenErrorMapping("SSO validation failed");
            when(ssoToken.validateSessionAndCode(validHeaders, validCartDto.getDealerCode()))
                    .thenReturn(Mono.just(tokenErrorMapping));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then - The error mapping function returns the TokenErrorMapping directly
            StepVerifier.create(result.cast(Object.class))
                    .expectNextMatches(TokenErrorMapping.class::isInstance)
                    .verifyComplete();

            verify(ssoToken).validateSessionAndCode(validHeaders, validCartDto.getDealerCode());
        }
    }

    @Nested
    @DisplayName("Fetch Cart Items Tests")
    class FetchCartItemsTests {

        @Test
        @DisplayName("fetchCartItems_ValidParameters_ReturnsCartItems")
        void fetchCartItems_ValidParameters_ReturnsCartItems() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchCartItems"));
                    });

            when(cartRepository.customFindByUserId(12345L))
                    .thenReturn(Flux.fromIterable(cartResponseList));

            when(responseMapper.setApiResponse(ERR_SUCCESS, cartResponseList, TRANS_FETCH_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, userId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).customFindByUserId(12345L);
            verify(responseMapper).setApiResponse(ERR_SUCCESS, cartResponseList, TRANS_FETCH_CART, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchCartItems_EmptyCartItems_ReturnsEmptyList")
        void fetchCartItems_EmptyCartItems_ReturnsEmptyList() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";
            List<CartResponse> emptyList = List.of();

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchCartItems"));
                    });

            when(cartRepository.customFindByUserId(12345L))
                    .thenReturn(Flux.empty());

            when(responseMapper.setApiResponse(ERR_SUCCESS, emptyList, TRANS_FETCH_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, userId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).customFindByUserId(12345L);
            verify(responseMapper).setApiResponse(ERR_SUCCESS, emptyList, TRANS_FETCH_CART, FALSE, validHeaders);
        }

        @Test
        @DisplayName("fetchCartItems_ValidationFailure_ReturnsError")
        void fetchCartItems_ValidationFailure_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, userId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(cartRepository);
        }

        @Test
        @DisplayName("fetchCartItems_RepositoryError_PropagatesError")
        void fetchCartItems_RepositoryError_PropagatesError() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchCartItems"));
                    });

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(cartRepository.customFindByUserId(12345L))
                    .thenReturn(Flux.error(repositoryException));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, userId);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(cartRepository).customFindByUserId(12345L);
        }

        @ParameterizedTest
        @ValueSource(strings = {"0", "-1"})
        @DisplayName("fetchCartItems_EdgeCaseUserIds_ProcessesCorrectly")
        void fetchCartItems_EdgeCaseUserIds_ProcessesCorrectly(String edgeUserId) {
            // Given
            String dealerCode = "DEALER001";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "fetchCartItems"));
                    });

            when(cartRepository.customFindByUserId(anyLong()))
                    .thenReturn(Flux.fromIterable(cartResponseList));

            when(responseMapper.setApiResponse(ERR_SUCCESS, cartResponseList, TRANS_FETCH_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, edgeUserId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchCartItems_InvalidUserIdFormat_HandledByValidation")
        void fetchCartItems_InvalidUserIdFormat_HandledByValidation() {
            // Given
            String dealerCode = "DEALER001";
            String invalidUserId = "invalid";

            // Mock validation failure for invalid userId format
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, invalidUserId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchCartItems_SSOTokenValidationFailure_ExecutesErrorMapping")
        void fetchCartItems_SSOTokenValidationFailure_ExecutesErrorMapping() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        // Input validation passes, but SSO validation should fail
                        Function<ErrorMapping, Mono<WsResponse>> errorMappingFunction = invocation.getArgument(1);
                        ErrorMapping errorMapping = mock(ErrorMapping.class);
                        return errorMappingFunction.apply(errorMapping);
                    });

            // Mock SSO token validation to return error response
            TokenErrorMapping tokenErrorMapping = new TokenErrorMapping("SSO validation failed");
            when(ssoToken.validateSessionAndCode(validHeaders, dealerCode))
                    .thenReturn(Mono.just(tokenErrorMapping));

            // When
            Mono<WsResponse> result = cartService.fetchCartItems(validHeaders, dealerCode, userId);

            // Then - The error mapping function returns the TokenErrorMapping directly
            StepVerifier.create(result.cast(Object.class))
                    .expectNextMatches(TokenErrorMapping.class::isInstance)
                    .verifyComplete();

            verify(ssoToken).validateSessionAndCode(validHeaders, dealerCode);
        }
    }

    @Nested
    @DisplayName("Delete Cart Items Tests")
    class DeleteCartItemsTests {

        @Test
        @DisplayName("deleteSavedCartItems_ValidParameters_DeletesSuccessfully")
        void deleteSavedCartItems_ValidParameters_DeletesSuccessfully() {
            // Given
            String dealerCode = "DEALER001";
            String savedCartId = "123";
            Delete deleteResult = new Delete(true);

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "delete cart"));
                    });

            when(cartRepository.deleteById(123L))
                    .thenReturn(Mono.empty());

            when(responseMapper.setApiResponse(ERR_SUCCESS, deleteResult, TRANS_DELETE_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.deleteSavedCartItems(validHeaders, dealerCode, savedCartId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).deleteById(123L);
            verify(responseMapper).setApiResponse(ERR_SUCCESS, deleteResult, TRANS_DELETE_CART, FALSE, validHeaders);
        }

        @Test
        @DisplayName("deleteSavedCartItems_ValidationFailure_ReturnsError")
        void deleteSavedCartItems_ValidationFailure_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            String savedCartId = "123";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = cartService.deleteSavedCartItems(validHeaders, dealerCode, savedCartId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
            verifyNoInteractions(cartRepository);
        }

        @Test
        @DisplayName("deleteSavedCartItems_RepositoryError_PropagatesError")
        void deleteSavedCartItems_RepositoryError_PropagatesError() {
            // Given
            String dealerCode = "DEALER001";
            String savedCartId = "123";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "delete cart"));
                    });

            RuntimeException repositoryException = new RuntimeException("Database error");
            when(cartRepository.deleteById(123L))
                    .thenReturn(Mono.error(repositoryException));

            // When
            Mono<WsResponse> result = cartService.deleteSavedCartItems(validHeaders, dealerCode, savedCartId);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();

            verify(cartRepository).deleteById(123L);
        }

        @Test
        @DisplayName("deleteSavedCartItems_InvalidCartIdFormat_HandledByValidation")
        void deleteSavedCartItems_InvalidCartIdFormat_HandledByValidation() {
            // Given
            String dealerCode = "DEALER001";
            String invalidCartId = "invalid";

            // Mock validation failure for invalid cartId format
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = cartService.deleteSavedCartItems(validHeaders, dealerCode, invalidCartId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(strings = {"0", "-1"})
        @DisplayName("deleteSavedCartItems_EdgeCaseValidCartId_ProcessesCorrectly")
        void deleteSavedCartItems_EdgeCaseValidCartId_ProcessesCorrectly(String edgeCaseCartId) {
            // Given
            String dealerCode = "DEALER001";
            Delete deleteResult = new Delete(true);

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), dealerCode, "delete cart"));
                    });

            when(cartRepository.deleteById(anyLong()))
                    .thenReturn(Mono.empty());

            when(responseMapper.setApiResponse(ERR_SUCCESS, deleteResult, TRANS_DELETE_CART, FALSE, validHeaders))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.deleteSavedCartItems(validHeaders, dealerCode, edgeCaseCartId);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(cartRepository).deleteById(Long.valueOf(edgeCaseCartId));
        }

        @Test
        @DisplayName("deleteSavedCartItems_SSOTokenValidationFailure_ExecutesErrorMapping")
        void deleteSavedCartItems_SSOTokenValidationFailure_ExecutesErrorMapping() {
            // Given
            String dealerCode = "DEALER001";
            String savedCartId = "123";

            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        // Input validation passes, but SSO validation should fail
                        Function<ErrorMapping, Mono<WsResponse>> errorMappingFunction = invocation.getArgument(1);
                        ErrorMapping errorMapping = mock(ErrorMapping.class);
                        return errorMappingFunction.apply(errorMapping);
                    });

            // Mock SSO token validation to return error response
            TokenErrorMapping tokenErrorMapping = new TokenErrorMapping("SSO validation failed");
            when(ssoToken.validateSessionAndCode(validHeaders, dealerCode))
                    .thenReturn(Mono.just(tokenErrorMapping));

            // When
            Mono<WsResponse> result = cartService.deleteSavedCartItems(validHeaders, dealerCode, savedCartId);

            // Then - The error mapping function returns the TokenErrorMapping directly
            StepVerifier.create(result.cast(Object.class))
                    .expectNextMatches(TokenErrorMapping.class::isInstance)
                    .verifyComplete();

            verify(ssoToken).validateSessionAndCode(validHeaders, dealerCode);
        }
    }

    @Nested
    @DisplayName("Integration and Edge Cases Tests")
    class IntegrationAndEdgeCasesTests {

        @Test
        @DisplayName("allMethods_NullHeaders_HandledBySharedValidation")
        void allMethods_NullHeaders_HandledBySharedValidation() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When & Then
            StepVerifier.create(cartService.add(null, validCartDto))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(cartService.fetchCartItems(null, "DEALER001", "123"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(cartService.deleteSavedCartItems(null, "DEALER001", "123"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared, times(3)).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
        }

        @Test
        @DisplayName("allMethods_EmptyHeaders_HandledBySharedValidation")
        void allMethods_EmptyHeaders_HandledBySharedValidation() {
            // Given
            Map<String, String> emptyHeaders = new HashMap<>();
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When & Then
            StepVerifier.create(cartService.add(emptyHeaders, validCartDto))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(cartService.fetchCartItems(emptyHeaders, "DEALER001", "123"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            StepVerifier.create(cartService.deleteSavedCartItems(emptyHeaders, "DEALER001", "123"))
                    .expectNext(mockErrorResponse)
                    .verifyComplete();

            verify(shared, times(3)).executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class));
        }

        @Test
        @DisplayName("concurrentOperations_MultipleCartOperations_HandledCorrectly")
        void concurrentOperations_MultipleCartOperations_HandledCorrectly() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), "DEALER001", "concurrent operation"));
                    });

            when(cartRepository.findByUserIdAndProductCode(anyLong(), anyString()))
                    .thenReturn(Mono.empty());
            when(cartRepository.customFindByUserId(anyLong()))
                    .thenReturn(Flux.fromIterable(cartResponseList));
            when(cartRepository.deleteById(anyLong()))
                    .thenReturn(Mono.empty());
            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.just(newCartEntity));
            when(responseMapper.setApiResponse(any(), any(), any(), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When - Execute concurrent operations
            Mono<WsResponse> addResult = cartService.add(validHeaders, validCartDto);
            Mono<WsResponse> fetchResult = cartService.fetchCartItems(validHeaders, "DEALER001", "123");
            Mono<WsResponse> deleteResult = cartService.deleteSavedCartItems(validHeaders, "DEALER001", "456");

            // Then
            StepVerifier.create(Mono.zip(addResult, fetchResult, deleteResult))
                    .expectNextMatches(tuple ->
                            tuple.getT1().equals(mockSuccessResponse) &&
                                    tuple.getT2().equals(mockSuccessResponse) &&
                                    tuple.getT3().equals(mockSuccessResponse))
                    .verifyComplete();
        }

        @Test
        @DisplayName("businessLogic_PartnerInfoCreation_VerifyCorrectValues")
        void businessLogic_PartnerInfoCreation_VerifyCorrectValues() {
            // Given
            when(shared.executeWithInputAndSessionValidation(any(), any(), any(), any(ValidationContext.class)))
                    .thenAnswer(invocation -> {
                        // Verify ValidationContext is created correctly
                        ValidationContext context = invocation.getArgument(3);
                        assert context != null;
                        assert context.headers().equals(validHeaders);
                        assert context.partnerInfo().getDealerCode().equals(validCartDto.getDealerCode());

                        Function<PartnerInfo, Mono<WsResponse>> businessLogic = invocation.getArgument(2);
                        return businessLogic.apply(new PartnerInfo(System.currentTimeMillis(), validCartDto.getDealerCode(), "add to cart"));
                    });

            when(cartRepository.findByUserIdAndProductCode(anyLong(), anyString()))
                    .thenReturn(Mono.empty());
            when(cartRepository.save(any(CartEntity.class)))
                    .thenReturn(Mono.just(newCartEntity));
            when(responseMapper.setApiResponse(any(), any(), any(), eq(FALSE), eq(validHeaders)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = cartService.add(validHeaders, validCartDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }
    }
}
