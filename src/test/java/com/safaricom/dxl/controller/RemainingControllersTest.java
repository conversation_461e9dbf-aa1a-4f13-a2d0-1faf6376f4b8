package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.PicklistNominationDto;
import com.safaricom.dxl.data.dto.SaveOrderItemsDto;
import com.safaricom.dxl.service.MiniStoreService;
import com.safaricom.dxl.service.PicklistNominationService;
import com.safaricom.dxl.service.SavedOrderService;
import com.safaricom.dxl.webflux.starter.model.WsHeader;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for remaining controllers
 * Tests MiniStoreController, PicklistNominationController, and SavedOrderController
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Remaining Controllers Unit Tests")
class RemainingControllersTest {

    // MiniStoreController dependencies
    @Mock
    private MiniStoreService miniStoreService;
    @InjectMocks
    private MiniStoreController miniStoreController;

    // PicklistNominationController dependencies
    @Mock
    private PicklistNominationService nominationService;
    @InjectMocks
    private PicklistNominationController picklistNominationController;

    // SavedOrderController dependencies
    @Mock
    private SavedOrderService savedOrderService;
    @InjectMocks
    private SavedOrderController savedOrderController;

    private Map<String, String> validHeaders;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put("X-Source-System", "test-system");
        validHeaders.put("Authorization", "Bearer test-token");
        
        mockSuccessResponse = createMockWsResponse("200", "Success");
        mockErrorResponse = createMockWsResponse("400", "Bad Request");
    }

    @Nested
    @DisplayName("MiniStoreController Tests")
    class MiniStoreControllerTests {

        @Test
        @DisplayName("fetchAllMiniStores_ValidDealerCode_ReturnsSuccess")
        void fetchAllMiniStores_ValidDealerCode_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(miniStoreService).fetchAllMiniStores(validHeaders, dealerCode);
        }

        @Test
        @DisplayName("fetchAllMiniStores_ServiceError_ReturnsError")
        void fetchAllMiniStores_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchAllMiniStores_ServiceException_PropagatesError")
        void fetchAllMiniStores_ServiceException_PropagatesError() {
            // Given
            String dealerCode = "DEALER001";
            RuntimeException exception = new RuntimeException("Service error");
            when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                    .thenReturn(Mono.error(exception));

            // When
            Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "INVALID_DEALER"})
        @DisplayName("fetchAllMiniStores_InvalidDealerCode_ProcessedByService")
        void fetchAllMiniStores_InvalidDealerCode_ProcessedByService(String invalidDealerCode) {
            // Given
            when(miniStoreService.fetchAllMiniStores(anyMap(), eq(invalidDealerCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, invalidDealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchAllMiniStores_EmptyHeaders_ProcessedByService")
        void fetchAllMiniStores_EmptyHeaders_ProcessedByService() {
            // Given
            String dealerCode = "DEALER001";
            Map<String, String> emptyHeaders = new HashMap<>();
            when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(emptyHeaders, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("PicklistNominationController Tests")
    class PicklistNominationControllerTests {

        @Test
        @DisplayName("addNominee_ValidInput_ReturnsSuccess")
        void addNominee_ValidInput_ReturnsSuccess() {
            // Given
            PicklistNominationDto nominationDto = createValidPicklistNominationDto();
            String dealerCode = "DEALER001";
            when(nominationService.addNominee(anyMap(), eq(nominationDto), eq(dealerCode)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.addNominee(validHeaders, nominationDto, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(nominationService).addNominee(validHeaders, nominationDto, dealerCode);
        }

        @Test
        @DisplayName("addNominee_ServiceError_ReturnsError")
        void addNominee_ServiceError_ReturnsError() {
            // Given
            PicklistNominationDto nominationDto = createValidPicklistNominationDto();
            String dealerCode = "DEALER001";
            when(nominationService.addNominee(anyMap(), eq(nominationDto), eq(dealerCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.addNominee(validHeaders, nominationDto, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("getActiveNominations_ValidParameters_ReturnsSuccess")
        void getActiveNominations_ValidParameters_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            int page = 0;
            int size = 10;
            when(nominationService.getActiveNominations(anyMap(), eq(dealerCode), eq(page), eq(size)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.getActiveNominations(validHeaders, dealerCode, page, size);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(nominationService).getActiveNominations(validHeaders, dealerCode, page, size);
        }

        @Test
        @DisplayName("getActiveNominations_DefaultPagination_ReturnsSuccess")
        void getActiveNominations_DefaultPagination_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            when(nominationService.getActiveNominations(anyMap(), eq(dealerCode), eq(0), eq(10)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.getActiveNominations(validHeaders, dealerCode, 0, 10);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(ints = {-1, 201, 1000})
        @DisplayName("getActiveNominations_InvalidSize_ProcessedByService")
        void getActiveNominations_InvalidSize_ProcessedByService(int invalidSize) {
            // Given - validation will handle size > 200, but we test service response
            String dealerCode = "DEALER001";
            when(nominationService.getActiveNominations(anyMap(), eq(dealerCode), anyInt(), eq(invalidSize)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.getActiveNominations(validHeaders, dealerCode, 0, invalidSize);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("retireNomination_ValidParameters_ReturnsSuccess")
        void retireNomination_ValidParameters_ReturnsSuccess() {
            // Given
            Long nominationId = 123L;
            String dealerCode = "DEALER001";
            when(nominationService.retireNomination(anyMap(), eq(nominationId), eq(dealerCode)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.retireNomination(validHeaders, nominationId, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(nominationService).retireNomination(validHeaders, nominationId, dealerCode);
        }

        @Test
        @DisplayName("retireNomination_ServiceError_ReturnsError")
        void retireNomination_ServiceError_ReturnsError() {
            // Given
            Long nominationId = 123L;
            String dealerCode = "DEALER001";
            when(nominationService.retireNomination(anyMap(), eq(nominationId), eq(dealerCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.retireNomination(validHeaders, nominationId, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(longs = {0L, -1L})
        @DisplayName("retireNomination_InvalidNominationId_ProcessedByService")
        void retireNomination_InvalidNominationId_ProcessedByService(Long invalidId) {
            // Given
            String dealerCode = "DEALER001";
            when(nominationService.retireNomination(anyMap(), eq(invalidId), eq(dealerCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = picklistNominationController.retireNomination(validHeaders, invalidId, dealerCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("SavedOrderController Tests")
    class SavedOrderControllerTests {

        @Test
        @DisplayName("add_ValidInput_ReturnsSuccess")
        void add_ValidInput_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            SaveOrderItemsDto saveOrderDto = createValidSaveOrderItemsDto();
            when(savedOrderService.addSavedOrder(anyMap(), eq(dealerCode), eq(saveOrderDto)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = savedOrderController.add(validHeaders, dealerCode, saveOrderDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(savedOrderService).addSavedOrder(validHeaders, dealerCode, saveOrderDto);
        }

        @Test
        @DisplayName("add_ServiceError_ReturnsError")
        void add_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            SaveOrderItemsDto saveOrderDto = createValidSaveOrderItemsDto();
            when(savedOrderService.addSavedOrder(anyMap(), eq(dealerCode), eq(saveOrderDto)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = savedOrderController.add(validHeaders, dealerCode, saveOrderDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchSavedOrder_ValidParameters_ReturnsSuccess")
        void fetchSavedOrder_ValidParameters_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";
            int pageNo = 0;
            int pageSize = 10;
            when(savedOrderService.fetchSavedOrder(anyMap(), eq(dealerCode), eq(userId), eq(pageNo), eq(pageSize)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = savedOrderController.fetchSavedOrder(validHeaders, dealerCode, userId, pageNo, pageSize);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(savedOrderService).fetchSavedOrder(validHeaders, dealerCode, userId, pageNo, pageSize);
        }

        @Test
        @DisplayName("fetchSavedOrder_DefaultPagination_ReturnsSuccess")
        void fetchSavedOrder_DefaultPagination_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";
            when(savedOrderService.fetchSavedOrder(anyMap(), eq(dealerCode), eq(userId), eq(0), eq(10)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = savedOrderController.fetchSavedOrder(validHeaders, dealerCode, userId, 0, 10);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchSavedOrderByOrderIdentifier_ValidParameters_ReturnsSuccess")
        void fetchSavedOrderByOrderIdentifier_ValidParameters_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            String orderIdentifier = "ORDER123";
            when(savedOrderService.fetchSavedOrderByOrderIdentifier(anyMap(), eq(dealerCode), eq(orderIdentifier)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = savedOrderController.fetchSavedOrderByOrderIdentifier(validHeaders, dealerCode, orderIdentifier);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(savedOrderService).fetchSavedOrderByOrderIdentifier(validHeaders, dealerCode, orderIdentifier);
        }

        @Test
        @DisplayName("fetchSavedOrderByOrderIdentifier_ServiceError_ReturnsError")
        void fetchSavedOrderByOrderIdentifier_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            String orderIdentifier = "ORDER123";
            when(savedOrderService.fetchSavedOrderByOrderIdentifier(anyMap(), eq(dealerCode), eq(orderIdentifier)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = savedOrderController.fetchSavedOrderByOrderIdentifier(validHeaders, dealerCode, orderIdentifier);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("deleteSavedOrder_ValidParameters_ReturnsSuccess")
        void deleteSavedOrder_ValidParameters_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            String orderIdentifier = "ORDER123";
            when(savedOrderService.deleteSavedOrder(anyMap(), eq(dealerCode), eq(orderIdentifier)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = savedOrderController.deleteSavedOrder(validHeaders, dealerCode, orderIdentifier);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(savedOrderService).deleteSavedOrder(validHeaders, dealerCode, orderIdentifier);
        }

        @Test
        @DisplayName("deleteSavedOrder_ServiceError_ReturnsError")
        void deleteSavedOrder_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            String orderIdentifier = "ORDER123";
            when(savedOrderService.deleteSavedOrder(anyMap(), eq(dealerCode), eq(orderIdentifier)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = savedOrderController.deleteSavedOrder(validHeaders, dealerCode, orderIdentifier);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "INVALID_ORDER"})
        @DisplayName("deleteSavedOrder_InvalidOrderIdentifier_ProcessedByService")
        void deleteSavedOrder_InvalidOrderIdentifier_ProcessedByService(String invalidOrderIdentifier) {
            // Given
            String dealerCode = "DEALER001";
            when(savedOrderService.deleteSavedOrder(anyMap(), eq(dealerCode), eq(invalidOrderIdentifier)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = savedOrderController.deleteSavedOrder(validHeaders, dealerCode, invalidOrderIdentifier);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("savedOrderOperations_ServiceException_PropagatesError")
        void savedOrderOperations_ServiceException_PropagatesError() {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";
            RuntimeException exception = new RuntimeException("Service error");
            when(savedOrderService.fetchSavedOrder(anyMap(), eq(dealerCode), eq(userId), anyInt(), anyInt()))
                    .thenReturn(Mono.error(exception));

            // When
            Mono<WsResponse> result = savedOrderController.fetchSavedOrder(validHeaders, dealerCode, userId, 0, 10);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();
        }

        @ParameterizedTest
        @ValueSource(ints = {-1, 1000})
        @DisplayName("fetchSavedOrder_EdgeCasePagination_ProcessedByService")
        void fetchSavedOrder_EdgeCasePagination_ProcessedByService(int edgeValue) {
            // Given
            String dealerCode = "DEALER001";
            String userId = "12345";
            when(savedOrderService.fetchSavedOrder(anyMap(), eq(dealerCode), eq(userId), eq(edgeValue), eq(edgeValue)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = savedOrderController.fetchSavedOrder(validHeaders, dealerCode, userId, edgeValue, edgeValue);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("Integration and Error Handling Tests")
    class IntegrationAndErrorHandlingTests {

        @Test
        @DisplayName("serviceMethodsCall_EnsureDependencyInjection_VerifyInteractions")
        void serviceMethodsCall_EnsureDependencyInjection_VerifyInteractions() {
            // Given - Verify that all controller methods properly delegate to service
            when(miniStoreService.fetchAllMiniStores(anyMap(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));
            when(nominationService.addNominee(anyMap(), any(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));
            when(savedOrderService.addSavedOrder(anyMap(), anyString(), any()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When - Execute methods from all controllers
            miniStoreController.fetchAllMiniStores(validHeaders, "DEALER001").block();
            picklistNominationController.addNominee(validHeaders, createValidPicklistNominationDto(), "DEALER001").block();
            savedOrderController.add(validHeaders, "DEALER001", createValidSaveOrderItemsDto()).block();

            // Then - Verify all service methods were called
            verify(miniStoreService).fetchAllMiniStores(validHeaders, "DEALER001");
            verify(nominationService).addNominee(validHeaders, createValidPicklistNominationDto(), "DEALER001");
            verify(savedOrderService).addSavedOrder(validHeaders, "DEALER001", createValidSaveOrderItemsDto());
        }

        @Test
        @DisplayName("concurrentRequests_MultipleControllers_HandledCorrectly")
        void concurrentRequests_MultipleControllers_HandledCorrectly() {
            // Given
            when(miniStoreService.fetchAllMiniStores(anyMap(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));
            when(nominationService.getActiveNominations(anyMap(), anyString(), anyInt(), anyInt()))
                    .thenReturn(Mono.just(mockSuccessResponse));
            when(savedOrderService.fetchSavedOrder(anyMap(), anyString(), anyString(), anyInt(), anyInt()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When - Simulate concurrent requests across different controllers
            Mono<WsResponse> miniStoreResult = miniStoreController.fetchAllMiniStores(validHeaders, "DEALER001");
            Mono<WsResponse> nominationResult = picklistNominationController.getActiveNominations(validHeaders, "DEALER001", 0, 10);
            Mono<WsResponse> savedOrderResult = savedOrderController.fetchSavedOrder(validHeaders, "DEALER001", "12345", 0, 10);

            // Then - All operations should complete successfully
            StepVerifier.create(Mono.zip(miniStoreResult, nominationResult, savedOrderResult))
                    .expectNextMatches(tuple -> 
                            tuple.getT1().equals(mockSuccessResponse) &&
                            tuple.getT2().equals(mockSuccessResponse) &&
                            tuple.getT3().equals(mockSuccessResponse))
                    .verifyComplete();
        }
    }

    // Helper methods for creating test data
    private PicklistNominationDto createValidPicklistNominationDto() {
        PicklistNominationDto dto = new PicklistNominationDto();
        // Set necessary fields based on actual PicklistNominationDto structure
        // This is a placeholder - adjust based on actual implementation
        return dto;
    }

    private SaveOrderItemsDto createValidSaveOrderItemsDto() {
        SaveOrderItemsDto dto = new SaveOrderItemsDto();
        // Set necessary fields based on actual SaveOrderItemsDto structure
        // This is a placeholder - adjust based on actual implementation
        return dto;
    }

    private WsResponse createMockWsResponse(String code, String message) {
        WsResponse response = new WsResponse();
        WsHeader header = new WsHeader();
        header.setResponseCode(Integer.parseInt(code));
        header.setCustomerMessage(message);
        response.setHeader(header);
        return response;
    }
}
