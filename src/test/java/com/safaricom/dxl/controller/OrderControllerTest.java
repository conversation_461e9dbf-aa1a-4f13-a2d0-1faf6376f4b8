package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.CartItemsDto;
import com.safaricom.dxl.service.OrderService;
import com.safaricom.dxl.webflux.starter.model.WsHeader;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for OrderController
 * Tests all endpoints with positive, negative, and edge case scenarios
 * Ensures 100% condition coverage as per coding standards
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OrderController Unit Tests")
class OrderControllerTest {

    @Mock
    private OrderService orderService;

    @InjectMocks
    private OrderController orderController;

    private Map<String, String> validHeaders;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put("X-Source-System", "test-system");
        validHeaders.put("Authorization", "Bearer test-token");

        mockSuccessResponse = createMockWsResponse("200", "Success");
        mockErrorResponse = createMockWsResponse("400", "Bad Request");
    }

    // Helper methods for creating test data
    private CartItemsDto createValidCartItemsDto() {
        CartItemsDto dto = new CartItemsDto();
        dto.setUserId(12345L);
        dto.setCartIds(List.of(1L, 2L, 3L));
        return dto;
    }

    private WsResponse createMockWsResponse(String code, String message) {
        WsResponse response = new WsResponse();
        WsHeader header = new WsHeader();
        header.setResponseCode(Integer.parseInt(code));
        header.setCustomerMessage(message);
        response.setHeader(header);
        return response;
    }

    @Nested
    @DisplayName("Create Order Tests")
    class CreateOrderTests {

        @Test
        @DisplayName("createOrder_ValidInput_ReturnsSuccess")
        void createOrder_ValidInput_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            CartItemsDto cartItemsDto = createValidCartItemsDto();
            when(orderService.createOrder(anyMap(), eq(dealerCode), eq(cartItemsDto)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.createOrder(validHeaders, dealerCode, cartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderService).createOrder(validHeaders, dealerCode, cartItemsDto);
        }

        @Test
        @DisplayName("createOrder_ServiceError_ReturnsError")
        void createOrder_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            CartItemsDto cartItemsDto = createValidCartItemsDto();
            when(orderService.createOrder(anyMap(), eq(dealerCode), eq(cartItemsDto)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.createOrder(validHeaders, dealerCode, cartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("createOrder_ServiceException_PropagatesError")
        void createOrder_ServiceException_PropagatesError() {
            // Given
            String dealerCode = "DEALER001";
            CartItemsDto cartItemsDto = createValidCartItemsDto();
            RuntimeException exception = new RuntimeException("Service error");
            when(orderService.createOrder(anyMap(), eq(dealerCode), eq(cartItemsDto)))
                    .thenReturn(Mono.error(exception));

            // When
            Mono<WsResponse> result = orderController.createOrder(validHeaders, dealerCode, cartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectError(RuntimeException.class)
                    .verify();
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "INVALID_DEALER"})
        @DisplayName("createOrder_InvalidDealerCode_ProcessedByService")
        void createOrder_InvalidDealerCode_ProcessedByService(String invalidDealerCode) {
            // Given
            CartItemsDto cartItemsDto = createValidCartItemsDto();
            when(orderService.createOrder(anyMap(), eq(invalidDealerCode), eq(cartItemsDto)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.createOrder(validHeaders, invalidDealerCode, cartItemsDto);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("Fetch Order by Dealer Code Tests")
    class FetchOrderByDealerCodeTests {

        @Test
        @DisplayName("fetchOrderByDealerCode_ValidParameters_ReturnsSuccess")
        void fetchOrderByDealerCode_ValidParameters_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            int pageNo = 0;
            int pageSize = 10;
            String from = "2024-01-01";
            String to = "2024-12-31";

            when(orderService.fetchOrderByDealerCode(anyMap(), eq(dealerCode),
                    eq(pageNo), eq(pageSize), eq(from), eq(to)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByDealerCode(
                    validHeaders, dealerCode, pageNo, pageSize, from, to);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderService).fetchOrderByDealerCode(validHeaders, dealerCode, pageNo, pageSize, from, to);
        }

        @Test
        @DisplayName("fetchOrderByDealerCode_DefaultPagination_ReturnsSuccess")
        void fetchOrderByDealerCode_DefaultPagination_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            when(orderService.fetchOrderByDealerCode(anyMap(), eq(dealerCode),
                    eq(0), eq(10), isNull(), isNull()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByDealerCode(
                    validHeaders, dealerCode, 0, 10, null, null);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("fetchOrderByDealerCode_ServiceError_ReturnsError")
        void fetchOrderByDealerCode_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            when(orderService.fetchOrderByDealerCode(anyMap(), eq(dealerCode),
                    anyInt(), anyInt(), any(), any()))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByDealerCode(
                    validHeaders, dealerCode, 0, 10, null, null);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(ints = {-1, 1000})
        @DisplayName("fetchOrderByDealerCode_EdgeCasePagination_ProcessedByService")
        void fetchOrderByDealerCode_EdgeCasePagination_ProcessedByService(int edgeValue) {
            // Given
            String dealerCode = "DEALER001";
            when(orderService.fetchOrderByDealerCode(anyMap(), eq(dealerCode),
                    eq(edgeValue), eq(edgeValue), any(), any()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByDealerCode(
                    validHeaders, dealerCode, edgeValue, edgeValue, null, null);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("Fetch Order by Order Number Tests")
    class FetchOrderByOrderNumberTests {

        @Test
        @DisplayName("fetchOrderByOrderNumber_ValidParameters_ReturnsSuccess")
        void fetchOrderByOrderNumber_ValidParameters_ReturnsSuccess() {
            // Given
            String dealerCode = "DEALER001";
            String orderNumber = "ORD123456";
            when(orderService.fetchOrderByOrderNumber(anyMap(), eq(dealerCode), eq(orderNumber)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByOrderNumber(
                    validHeaders, dealerCode, orderNumber);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderService).fetchOrderByOrderNumber(validHeaders, dealerCode, orderNumber);
        }

        @Test
        @DisplayName("fetchOrderByOrderNumber_ServiceError_ReturnsError")
        void fetchOrderByOrderNumber_ServiceError_ReturnsError() {
            // Given
            String dealerCode = "DEALER001";
            String orderNumber = "ORD123456";
            when(orderService.fetchOrderByOrderNumber(anyMap(), eq(dealerCode), eq(orderNumber)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByOrderNumber(
                    validHeaders, dealerCode, orderNumber);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "INVALID_ORDER_123"})
        @DisplayName("fetchOrderByOrderNumber_InvalidOrderNumber_ProcessedByService")
        void fetchOrderByOrderNumber_InvalidOrderNumber_ProcessedByService(String invalidOrderNumber) {
            // Given
            String dealerCode = "DEALER001";
            when(orderService.fetchOrderByOrderNumber(anyMap(), eq(dealerCode), eq(invalidOrderNumber)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.fetchOrderByOrderNumber(
                    validHeaders, dealerCode, invalidOrderNumber);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("Export to S3 Tests")
    class ExportToS3Tests {

        @Test
        @DisplayName("exportToS3_ValidParameters_ReturnsSuccess")
        void exportToS3_ValidParameters_ReturnsSuccess() {
            // Given
            String shortCode = "SC001";
            String startDate = "2024-01-01";
            String endDate = "2024-12-31";
            when(orderService.exportToS3(anyMap(), eq(shortCode), eq(startDate), eq(endDate)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.exportToS3(validHeaders, shortCode, startDate, endDate);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderService).exportToS3(validHeaders, shortCode, startDate, endDate);
        }

        @Test
        @DisplayName("exportToS3_ServiceError_ReturnsError")
        void exportToS3_ServiceError_ReturnsError() {
            // Given
            String shortCode = "SC001";
            String startDate = "2024-01-01";
            String endDate = "2024-12-31";
            when(orderService.exportToS3(anyMap(), eq(shortCode), eq(startDate), eq(endDate)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.exportToS3(validHeaders, shortCode, startDate, endDate);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "INVALID_DATE"})
        @DisplayName("exportToS3_InvalidDates_ProcessedByService")
        void exportToS3_InvalidDates_ProcessedByService(String invalidDate) {
            // Given
            String shortCode = "SC001";
            when(orderService.exportToS3(anyMap(), eq(shortCode), eq(invalidDate), eq(invalidDate)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.exportToS3(validHeaders, shortCode, invalidDate, invalidDate);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }
    }

    @Nested
    @DisplayName("Fetch Excel URL Tests")
    class FetchExcelUrlTests {

        @Test
        @DisplayName("fetchExcelUrl_ValidShortCode_ReturnsSuccess")
        void fetchExcelUrl_ValidShortCode_ReturnsSuccess() {
            // Given
            String shortCode = "SC001";
            when(orderService.fetchExcelUrl(anyMap(), eq(shortCode)))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When
            Mono<WsResponse> result = orderController.fetchExcelUrl(validHeaders, shortCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();

            verify(orderService).fetchExcelUrl(validHeaders, shortCode);
        }

        @Test
        @DisplayName("fetchExcelUrl_ServiceError_ReturnsError")
        void fetchExcelUrl_ServiceError_ReturnsError() {
            // Given
            String shortCode = "SC001";
            when(orderService.fetchExcelUrl(anyMap(), eq(shortCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.fetchExcelUrl(validHeaders, shortCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }

        @ParameterizedTest
        @ValueSource(strings = {"", " ", "INVALID_SHORT_CODE"})
        @DisplayName("fetchExcelUrl_InvalidShortCode_ProcessedByService")
        void fetchExcelUrl_InvalidShortCode_ProcessedByService(String invalidShortCode) {
            // Given
            when(orderService.fetchExcelUrl(anyMap(), eq(invalidShortCode)))
                    .thenReturn(Mono.just(mockErrorResponse));

            // When
            Mono<WsResponse> result = orderController.fetchExcelUrl(validHeaders, invalidShortCode);

            // Then
            StepVerifier.create(result)
                    .expectNext(mockErrorResponse)
                    .verifyComplete();
        }
    }
}
