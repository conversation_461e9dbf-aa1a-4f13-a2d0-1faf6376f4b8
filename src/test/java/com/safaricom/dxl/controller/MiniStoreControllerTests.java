package com.safaricom.dxl.controller;

import com.safaricom.dxl.service.MiniStoreService;
import com.safaricom.dxl.webflux.starter.model.WsHeader;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("MiniStoreController Unit Tests")
class MiniStoreControllerTests {
    @Mock
    private MiniStoreService miniStoreService;
    @InjectMocks
    private MiniStoreController miniStoreController;

    private Map<String, String> validHeaders;
    private WsResponse mockSuccessResponse;
    private WsResponse mockErrorResponse;

    @BeforeEach
    void setUp() {
        // Given
        validHeaders = new HashMap<>();
        validHeaders.put("X-Conversation-ID", "test-conversation-id");
        validHeaders.put("X-Source-System", "test-system");
        validHeaders.put("Authorization", "Bearer test-token");

        mockSuccessResponse = createMockWsResponse("200", "Success");
        mockErrorResponse = createMockWsResponse("400", "Bad Request");
    }

    @Test
    @DisplayName("fetchAllMiniStores_ValidDealerCode_ReturnsSuccess")
    void fetchAllMiniStores_ValidDealerCode_ReturnsSuccess() {
        // Given
        String dealerCode = "DEALER001";
        when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                .thenReturn(Mono.just(mockSuccessResponse));

        // When
        Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, dealerCode);

        // Then
        StepVerifier.create(result)
                .expectNext(mockSuccessResponse)
                .verifyComplete();

        verify(miniStoreService).fetchAllMiniStores(validHeaders, dealerCode);
    }

    @Test
    @DisplayName("fetchAllMiniStores_ServiceError_ReturnsError")
    void fetchAllMiniStores_ServiceError_ReturnsError() {
        // Given
        String dealerCode = "DEALER001";
        when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                .thenReturn(Mono.just(mockErrorResponse));

        // When
        Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, dealerCode);

        // Then
        StepVerifier.create(result)
                .expectNext(mockErrorResponse)
                .verifyComplete();
    }

    @Test
    @DisplayName("fetchAllMiniStores_ServiceException_PropagatesError")
    void fetchAllMiniStores_ServiceException_PropagatesError() {
        // Given
        String dealerCode = "DEALER001";
        RuntimeException exception = new RuntimeException("Service error");
        when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                .thenReturn(Mono.error(exception));

        // When
        Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, dealerCode);

        // Then
        StepVerifier.create(result)
                .expectError(RuntimeException.class)
                .verify();
    }

    @ParameterizedTest
    @ValueSource(strings = {"", " ", "INVALID_DEALER"})
    @DisplayName("fetchAllMiniStores_InvalidDealerCode_ProcessedByService")
    void fetchAllMiniStores_InvalidDealerCode_ProcessedByService(String invalidDealerCode) {
        // Given
        when(miniStoreService.fetchAllMiniStores(anyMap(), eq(invalidDealerCode)))
                .thenReturn(Mono.just(mockErrorResponse));

        // When
        Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(validHeaders, invalidDealerCode);

        // Then
        StepVerifier.create(result)
                .expectNext(mockErrorResponse)
                .verifyComplete();
    }

    @Test
    @DisplayName("fetchAllMiniStores_EmptyHeaders_ProcessedByService")
    void fetchAllMiniStores_EmptyHeaders_ProcessedByService() {
        // Given
        String dealerCode = "DEALER001";
        Map<String, String> emptyHeaders = new HashMap<>();
        when(miniStoreService.fetchAllMiniStores(anyMap(), eq(dealerCode)))
                .thenReturn(Mono.just(mockErrorResponse));

        // When
        Mono<WsResponse> result = miniStoreController.fetchAllMiniStores(emptyHeaders, dealerCode);

        // Then
        StepVerifier.create(result)
                .expectNext(mockErrorResponse)
                .verifyComplete();
    }

    @Nested
    @DisplayName("Integration and Error Handling Tests")
    class IntegrationAndErrorHandlingTests {

        @Test
        @DisplayName("serviceMethodsCall_EnsureDependencyInjection_VerifyInteractions")
        void serviceMethodsCall_EnsureDependencyInjection_VerifyInteractions() {
            // Given - Verify that all controller methods properly delegate to service
            when(miniStoreService.fetchAllMiniStores(anyMap(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When - Execute methods from all controllers
            miniStoreController.fetchAllMiniStores(validHeaders, "DEALER001").block();

            // Then - Verify all service methods were called
            verify(miniStoreService).fetchAllMiniStores(validHeaders, "DEALER001");
        }

        @Test
        @DisplayName("concurrentRequests_MultipleControllers_HandledCorrectly")
        void concurrentRequests_MultipleControllers_HandledCorrectly() {
            // Given
            when(miniStoreService.fetchAllMiniStores(anyMap(), anyString()))
                    .thenReturn(Mono.just(mockSuccessResponse));

            // When - Simulate concurrent requests across different controllers
            Mono<WsResponse> miniStoreResult = miniStoreController.fetchAllMiniStores(validHeaders, "DEALER001");

            // Then - All operations should complete successfully
            StepVerifier.create(miniStoreResult)
                    .expectNext(mockSuccessResponse)
                    .verifyComplete();
        }
    }

    private WsResponse createMockWsResponse(String code, String message) {
        WsResponse response = new WsResponse();
        WsHeader header = new WsHeader();
        header.setResponseCode(Integer.parseInt(code));
        header.setCustomerMessage(message);
        response.setHeader(header);
        return response;
    }
}
